# 组件库文档

## 概述

本文档详细描述了从 Figma 设计中提取的所有 UI 组件，包括其结构、样式和使用方法。这些组件采用了现代的毛玻璃效果（Glassmorphism），通过背景模糊和半透明效果创造出优雅的视觉体验。

## 毛玻璃效果系统

### 核心技术

所有组件都采用了毛玻璃效果（Glassmorphism），主要特征：

- **背景模糊**: 使用 `backdrop-filter: blur()` 实现
- **半透明背景**: 使用 `rgba()` 颜色值
- **细边框**: 增强玻璃质感
- **柔和阴影**: 创造浮起效果

### 毛玻璃强度等级

```css
/* 轻微毛玻璃 */
.glass-light {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
}

/* 中等毛玻璃 */
.glass-medium {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.15);
}

/* 强烈毛玻璃 */
.glass-strong {
  backdrop-filter: blur(30px);
  background: rgba(45, 45, 63, 0.8);
}
```

## 组件列表

### 1. Logo 组件

**用途**: 品牌标识展示

**HTML 结构**:
```html
<div class="logo-component">
    <div class="logo-icon">
        <!-- SVG 图标 -->
    </div>
    <div class="logo-text">
        <div class="logo-text-top"><!-- 上层文字 SVG --></div>
        <div class="logo-text-bottom"><!-- 下层文字 SVG --></div>
    </div>
</div>
```

**样式特点**:
- 图标尺寸: 24x19px
- 文字分为上下两层，各 8px 高
- 背景: 半透明黑色 `rgba(0, 0, 0, 0.3)`
- 边框: 浅色边框 `var(--dark-line-1)`

### 2. 输入框组件

**用途**: 用户信息输入

#### 2.1 手机号输入框

**HTML 结构**:
```html
<div class="input-component input-phone">
    <div class="input-icon"><!-- 手机图标 --></div>
    <div class="input-divider"></div>
    <input type="tel" placeholder="请输入手机号" class="input-field">
</div>
```

#### 2.2 验证码输入框

**HTML 结构**:
```html
<div class="input-component input-code">
    <div class="input-icon"><!-- 安全图标 --></div>
    <div class="input-divider"></div>
    <input type="text" placeholder="请输入验证码" class="input-field">
    <button class="send-code-btn">发送验证码</button>
</div>
```

**样式特点**:
- 背景: 半透明黑色 `rgba(0, 0, 0, 0.3)`
- 边框: 半透明白色 `rgba(255, 255, 255, 0.1)`
- 图标尺寸: 24x24px，透明度 60%
- 分隔线: 1px 宽，20px 高，透明度 20%
- 占位符文字: 透明度 30%

### 3. 按钮组件

**用途**: 用户操作触发

#### 3.1 主要按钮

**HTML 结构**:
```html
<button class="btn btn-primary">
    <span>登录</span>
    <svg><!-- 箭头图标 --></svg>
</button>
```

**样式特点**:
- 背景: 高亮色 `var(--text-highlight)`
- 悬停效果: 颜色变深，向上移动 1px
- 内边距: 16px 24px
- 圆角: 8px

### 4. 图标按钮组件

**用途**: 快速操作按钮

#### 4.1 建联按钮

**HTML 结构**:
```html
<div class="icon-btn icon-btn-connect">
    <svg><!-- 链接图标 --></svg>
</div>
```

**样式特点**:
- 尺寸: 48x48px
- 背景: 青色 `#3bb1b7`
- 形状: 圆形
- 悬停效果: 放大 1.05 倍

#### 4.2 回复按钮

**HTML 结构**:
```html
<div class="icon-btn icon-btn-reply">
    <svg><!-- 聊天图标 --></svg>
</div>
```

**样式特点**:
- 尺寸: 48x48px
- 背景: 绿色 `#3ab764`
- 形状: 圆形

### 5. 状态标签组件

**用途**: 数据状态展示

#### 5.1 提升状态

**HTML 结构**:
```html
<div class="status-tag status-up">
    <svg><!-- 向上箭头 --></svg>
    <span>0.00%</span>
</div>
```

**样式特点**:
- 背景: 半透明深色 `rgba(10, 2, 41, 0.1)`
- 文字颜色: 青色 `rgba(0, 255, 229, 0.6)`
- 图标: 向上箭头，青色描边

#### 5.2 下降状态

**HTML 结构**:
```html
<div class="status-tag status-down">
    <svg><!-- 向下箭头 --></svg>
    <span>0.00%</span>
</div>
```

**样式特点**:
- 背景: 半透明深色 `rgba(10, 2, 41, 0.1)`
- 文字颜色: 橙色 `rgba(255, 119, 0, 0.6)`
- 图标: 向下箭头，橙色描边

### 6. 导航列表项组件

**用途**: 侧边栏导航

**HTML 结构**:
```html
<div class="nav-item nav-item-selected">
    <div class="nav-icon">
        <svg><!-- 仪表盘图标 --></svg>
    </div>
    <span class="nav-text">仪表盘</span>
</div>
```

**样式特点**:
- 选中状态: 渐变背景，从左到右透明度递减
- 边框: 半透明白色 `rgba(255, 255, 255, 0.15)`
- 悬停效果: 半透明白色背景
- 图标尺寸: 24x24px

## 颜色规范

### 新增颜色变量

```css
/* 状态颜色 */
--status-up: rgba(0, 255, 229, 0.6);     /* 提升状态 */
--status-down: rgba(255, 119, 0, 0.6);   /* 下降状态 */
--connect-color: #3bb1b7;                /* 建联按钮 */
--reply-color: #3ab764;                  /* 回复按钮 */

/* 输入框颜色 */
--input-bg: rgba(0, 0, 0, 0.3);          /* 输入框背景 */
--input-border: rgba(255, 255, 255, 0.1); /* 输入框边框 */
--input-divider: rgba(255, 255, 255, 0.2); /* 分隔线 */
--placeholder: rgba(255, 255, 255, 0.3);  /* 占位符文字 */
```

## 图标规范

### 图标尺寸标准
- **小图标**: 16x16px (状态标签)
- **标准图标**: 24x24px (输入框、导航)
- **大图标**: 48x48px (图标按钮)

### 图标样式
- **描边宽度**: 1.5px
- **端点样式**: 圆角 (stroke-linecap="round")
- **连接样式**: 圆角 (stroke-linejoin="round")
- **透明度**: 60%-90% (根据重要性)

## 交互效果

### 悬停效果
```css
/* 按钮悬停 */
.btn:hover {
  transform: translateY(-1px);
  background: #5a58e8;
}

/* 图标按钮悬停 */
.icon-btn:hover {
  transform: scale(1.05);
}

/* 导航项悬停 */
.nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
}
```

### 过渡动画
- **持续时间**: 0.2s - 0.3s
- **缓动函数**: ease
- **属性**: transform, background, color

## 响应式适配

### 移动端调整
- 输入框宽度: 100%
- 按钮间距: 减少为 8px
- 图标按钮尺寸: 保持 48px (触摸友好)
- 文字大小: 保持不变 (可读性)

## 使用指南

### 组件组合原则
1. **保持一致性**: 使用相同的颜色变量和间距
2. **层次清晰**: 通过透明度和大小区分重要性
3. **交互反馈**: 所有可交互元素都有悬停效果
4. **无障碍性**: 确保足够的对比度和触摸目标大小

### 扩展新组件
1. 继承基础样式变量
2. 遵循现有的命名规范
3. 添加适当的交互效果
4. 确保响应式兼容性

### AI 生成新界面时的参考
1. 使用本文档中定义的组件作为基础
2. 保持相同的视觉风格和交互模式
3. 复用现有的颜色和间距变量
4. 确保新组件与现有设计和谐统一
