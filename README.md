# 合作管理系统 - Figma 设计复刻

## 项目概述

这是一个基于 Figma 设计原型的完整 HTML 界面复刻项目，旨在为后续 AI 编程提供设计规范和参考示例。

## 项目结构

```
原型 3/
├── index.html          # 主要的 HTML 界面文件
├── styles.css          # 完整的 CSS 样式文件
├── design-system.md    # 详细的设计规范指南
└── README.md          # 项目说明文档
```

## 功能特性

### 界面组件
- **复杂背景渐变**: 多层椭圆形渐变效果，使用 SVG 滤镜实现
- **通知卡片**: 显示 AI 助手推荐和合作邮件发送状态
- **合作漏斗卡片**: 展示合作进度的统计数据
- **响应式设计**: 适配桌面端和移动端

### 技术实现
- **纯 HTML + CSS**: 无依赖的原生实现
- **SVG 图标**: 矢量图标确保清晰度
- **CSS 变量**: 完整的设计系统变量定义
- **现代 CSS**: 使用 Flexbox、Grid 等现代布局技术

## 设计系统

### 颜色规范
- **文本颜色**: 三级白色文本层次 (#ffffffe5, #ffffff99, #ffffff4d)
- **背景颜色**: 深色主题 (#2d2d3f)
- **渐变颜色**: 蓝紫色系渐变 (#3234CA, #3D83F5, #9B38C6, #668CFF, #4848EF)
- **边框颜色**: 半透明白色边框 (#ffffff1a, #ffffff26)

### 字体规范
- **字体族**: Noto Sans SC (中文优化)
- **字体大小**: 12px - 18px 四级尺寸系统
- **字体权重**: Regular(400), Medium(500), SemiBold(600)

### 间距规范
- **间距系统**: 4px - 32px 五级间距系统
- **组件间距**: 统一使用设计变量

## 使用方法

### 直接查看
1. 在浏览器中打开 `index.html` 文件
2. 查看完整的界面效果

### 作为 AI 编程参考
1. 阅读 `design-system.md` 了解完整的设计规范
2. 参考 `styles.css` 中的组件样式实现
3. 使用设计系统变量确保新功能的一致性

## 设计原型信息

- **Figma 链接**: https://www.figma.com/design/r7oze007T2P3ISpwPUR6aY/Untitled?node-id=1-429
- **节点 ID**: 1:429
- **设计主题**: 合作管理系统界面
- **设计风格**: 深色主题，现代简约

## 技术特点

### 背景渐变实现
使用多个 SVG 椭圆叠加，通过 CSS 绝对定位和 transform 实现复杂的渐变背景效果：

```css
.bg-ellipse {
  position: absolute;
  pointer-events: none;
}

.bg-ellipse svg {
  width: 100%;
  height: 100%;
  display: block;
}
```

### 组件化设计
每个界面元素都采用模块化设计，便于复用和扩展：

- **卡片组件**: 统一的卡片样式和结构
- **通知项组件**: 可复用的通知列表项
- **统计项组件**: 数据展示的标准格式

### 响应式适配
针对不同屏幕尺寸进行了优化：

```css
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
```

## 扩展指南

### 添加新功能
1. 遵循 `design-system.md` 中的设计规范
2. 使用现有的 CSS 变量和组件样式
3. 保持视觉一致性和用户体验

### 修改样式
1. 优先修改 CSS 变量而非具体样式
2. 确保修改不会破坏现有组件
3. 测试响应式效果

### AI 编程建议
1. 将 `design-system.md` 作为上下文提供给 AI
2. 参考现有组件的 HTML 结构和 CSS 实现
3. 使用相同的命名规范和代码风格

## 浏览器兼容性

- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **CSS 特性**: CSS Grid, Flexbox, CSS Variables, SVG Filters
- **字体支持**: 需要网络连接加载 Google Fonts

## 许可证

本项目仅用于学习和参考目的，基于 Figma 设计原型创建。

## 更新日志

### v1.0.0 (2025-01-14)
- 完成 Figma 设计的完整复刻
- 创建完整的设计系统文档
- 实现响应式设计
- 添加动画效果和交互反馈

## 联系信息

如有问题或建议，请参考设计系统文档或查看源代码注释。
