<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合作管理系统 - Figma 设计复刻</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 背景层 -->
        <div class="bg-home">
            <!-- 背景渐变椭圆 1 -->
            <div class="bg-ellipse bg-ellipse-1">
                <svg viewBox="0 0 2491 2491" fill="none">
                    <g filter="url(#filter0_f_1_7179)" opacity="0.5">
                        <circle cx="1245.5" cy="1245.5" fill="#3234CA" r="645.5"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_7179" x="0" y="0" width="2491" height="2491" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_7179" stdDeviation="300"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 2 -->
            <div class="bg-ellipse bg-ellipse-2">
                <svg viewBox="0 0 2491 2433" fill="none">
                    <g filter="url(#filter0_f_1_7161)" opacity="0.25">
                        <path d="M1891 1186.54C1891 1543.04 1602 1832.04 1245.5 1832.04C889 1832.04 600 1543.04 600 1186.54C600 830.039 834.5 537.538 1263.5 611.539C1692.5 685.539 1891 830.039 1891 1186.54Z" fill="#3D83F5"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_7161" x="0" y="0" width="2491" height="2432.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_7161" stdDeviation="300"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 3 -->
            <div class="bg-ellipse bg-ellipse-3">
                <svg viewBox="0 0 1506 1501" fill="none">
                    <g filter="url(#filter0_f_1_6904)" opacity="0.55">
                        <path d="M1206 747.021C1206 997.206 1003.18 1200.02 753 1200.02C502.815 1200.02 300 997.206 300 747.021C300 496.836 334 231.521 700 316.021C1066 400.521 1206 496.836 1206 747.021Z" fill="#9B38C6"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_6904" x="0" y="0" width="1506" height="1500.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_6904" stdDeviation="150"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 4 -->
            <div class="bg-ellipse bg-ellipse-4">
                <svg viewBox="0 0 1123 889" fill="none">
                    <g filter="url(#filter0_f_1_6983)" opacity="0.8">
                        <path d="M823 556.532C823 647.482 604.406 518.479 456.178 518.479C307.95 518.479 300 555.629 300 464.679C300 373.729 420.162 300 568.39 300C716.618 300 823 465.582 823 556.532Z" fill="#668CFF"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_6983" x="0" y="0" width="1123" height="889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_6983" stdDeviation="150"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 5 -->
            <div class="bg-ellipse bg-ellipse-5">
                <svg viewBox="0 0 1100 1050" fill="none">
                    <g filter="url(#filter0_if_1_7000)">
                        <ellipse cx="550" cy="525" fill="#4848EF" fill-opacity="0.1" rx="370" ry="345"/>
                    </g>
                    <defs>
                        <filter id="filter0_if_1_7000" x="0" y="0" width="1100" height="1050" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                            <feOffset dy="-12"/>
                            <feGaussianBlur stdDeviation="45"/>
                            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.325191 0 0 0 0 0.266598 0 0 0 0 0.969709 0 0 0 1 0"/>
                            <feBlend in2="shape" mode="normal" result="effect1_innerShadow_1_7000"/>
                            <feGaussianBlur result="effect2_foregroundBlur_1_7000" stdDeviation="90"/>
                        </filter>
                    </defs>
                </svg>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 通知卡片 -->
            <div class="notification-card">
                <div class="card-header">
                    <div class="header-content">
                        <div class="icon-container">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <path d="M25.6188 8.64344C25.5033 8.37673 25.312 8.14984 25.0687 7.99099C24.8253 7.83214 24.5406 7.74833 24.25 7.75H7.75C7.45969 7.75057 7.17578 7.83538 6.93271 7.99413C6.68965 8.15288 6.49787 8.37876 6.38066 8.64436C6.26344 8.90996 6.22582 9.20387 6.27235 9.49043C6.31888 9.77699 6.44757 10.0439 6.64281 10.2588L6.65031 10.2672L13 17.0472V24.25C12.9999 24.5215 13.0736 24.7879 13.213 25.0208C13.3524 25.2537 13.5525 25.4445 13.7918 25.5726C14.0311 25.7008 14.3007 25.7616 14.5719 25.7486C14.8431 25.7356 15.1056 25.6493 15.3316 25.4987L18.3316 23.4981C18.5372 23.3611 18.7058 23.1755 18.8225 22.9576C18.9391 22.7398 19.0001 22.4965 19 22.2494V17.0472L25.3506 10.2672L25.3581 10.2588C25.5554 10.0449 25.6854 9.77761 25.7317 9.49034C25.7781 9.20308 25.7388 8.90852 25.6188 8.64344ZM17.7044 16.2419C17.5746 16.3794 17.5016 16.5609 17.5 16.75V22.2494L14.5 24.25V16.75C14.5001 16.5596 14.4277 16.3762 14.2975 16.2372L7.75 9.25H24.25L17.7044 16.2419Z" fill="white" fill-opacity="0.9"/>
                            </svg>
                        </div>
                        <h2 class="card-title">合作</h2>
                    </div>
                </div>

                <div class="notification-list">
                    <!-- 通知项 1 -->
                    <div class="notification-item">
                        <div class="notification-avatar">
                            <div class="avatar-icon">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <circle cx="10" cy="10" r="8" stroke="white" stroke-opacity="0.9" stroke-width="1.25"/>
                                    <path d="M6 10.5L8.5 13L14 7.5" stroke="white" stroke-opacity="0.9" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        </div>
                        <div class="notification-content">
                            <div class="notification-text">AI 助手推荐了 3 位新的合适博主</div>
                            <div class="notification-time">昨天 14:34</div>
                        </div>
                    </div>

                    <!-- 通知项 2 -->
                    <div class="notification-item">
                        <div class="notification-avatar">
                            <div class="avatar-icon">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <circle cx="10" cy="10" r="8" stroke="white" stroke-opacity="0.9" stroke-width="1.25"/>
                                    <path d="M6 10.5L8.5 13L14 7.5" stroke="white" stroke-opacity="0.9" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        </div>
                        <div class="notification-content">
                            <div class="notification-text">你向 AI TV 发送了合作邮件</div>
                            <div class="notification-time">2025/06/23 10:14</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 合作漏斗卡片 -->
            <div class="funnel-card">
                <div class="card-header">
                    <div class="header-content">
                        <div class="icon-container">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <path d="M25.6188 8.64344C25.5033 8.37673 25.312 8.14984 25.0687 7.99099C24.8253 7.83214 24.5406 7.74833 24.25 7.75H7.75C7.45969 7.75057 7.17578 7.83538 6.93271 7.99413C6.68965 8.15288 6.49787 8.37876 6.38066 8.64436C6.26344 8.90996 6.22582 9.20387 6.27235 9.49043C6.31888 9.77699 6.44757 10.0439 6.64281 10.2588L6.65031 10.2672L13 17.0472V24.25C12.9999 24.5215 13.0736 24.7879 13.213 25.0208C13.3524 25.2537 13.5525 25.4445 13.7918 25.5726C14.0311 25.7008 14.3007 25.7616 14.5719 25.7486C14.8431 25.7356 15.1056 25.6493 15.3316 25.4987L18.3316 23.4981C18.5372 23.3611 18.7058 23.1755 18.8225 22.9576C18.9391 22.7398 19.0001 22.4965 19 22.2494V17.0472L25.3506 10.2672L25.3581 10.2588C25.5554 10.0449 25.6854 9.77761 25.7317 9.49034C25.7781 9.20308 25.7388 8.90852 25.6188 8.64344ZM17.7044 16.2419C17.5746 16.3794 17.5016 16.5609 17.5 16.75V22.2494L14.5 24.25V16.75C14.5001 16.5596 14.4277 16.3762 14.2975 16.2372L7.75 9.25H24.25L17.7044 16.2419Z" fill="white" fill-opacity="0.9"/>
                            </svg>
                        </div>
                        <h2 class="card-title">合作漏斗</h2>
                        <div class="header-dropdown">
                            <span>本月</span>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M4 6L8 10L12 6" stroke="white" stroke-opacity="0.9" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="funnel-stats">
                    <div class="stat-item">
                        <span class="stat-label">已联系</span>
                        <span class="stat-value">231</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">沟通中</span>
                        <span class="stat-value">53</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">已确认</span>
                        <span class="stat-value">5774</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
