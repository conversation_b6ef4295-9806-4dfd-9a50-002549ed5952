# 设计系统规范 - Design System Guide

## 概述

本设计系统基于 Figma 原型创建，包含完整的颜色、字体、间距、组件规范。适用于 AI 编程参考，确保界面设计的一致性和可维护性。

## 颜色系统 - Color System

### 主要颜色变量

```css
/* 文本颜色 - Text Colors */
--white-text-1: #ffffffe5;     /* 主要文本，透明度 90% */
--white-text-2: #ffffff99;     /* 次要文本，透明度 60% */
--white-text-3: #ffffff4d;     /* 辅助文本，透明度 30% */
--dark-text-1: #000000d9;      /* 深色文本，透明度 85% */

/* 背景颜色 - Background Colors */
--dark-bg: #2d2d3f;            /* 主要深色背景 */
--dark-bg-alt: #3a3a3a;        /* 替代深色背景 */
--input-bg: rgba(0, 0, 0, 0.3); /* 输入框背景 */
--card-bg: rgba(10, 2, 41, 0.1); /* 卡片背景 */

/* 主题颜色 - Theme Colors */
--text-highlight: #7270ff;     /* 高亮颜色 */
--red: #d04747;                 /* 错误/警告红色 */
--purple: #9B38C6;              /* 紫色主题 */

/* 功能颜色 - Functional Colors */
--success-green: #3ab764;       /* 成功绿色 */
--info-blue: #3bb1b7;          /* 信息蓝色 */
--warning-orange: #FF7700;      /* 警告橙色 */
--accent-cyan: #00FFE6;         /* 强调青色 */
```

### 颜色使用规则

1. **主要文本**: 使用 `--white-text-1` 用于标题和重要信息
2. **次要文本**: 使用 `--white-text-2` 用于描述性文本
3. **辅助文本**: 使用 `--white-text-3` 用于时间戳和不重要信息
4. **功能色彩**: 
   - 成功: `--success-green`
   - 信息: `--info-blue`
   - 警告: `--warning-orange`
   - 错误: `--red`
   - 提升: `--accent-cyan`

### 状态颜色系统

```css
/* 成功状态 */
--success-bg: rgba(58, 183, 100, 0.1);
--success-border: rgba(58, 183, 100, 0.2);
--success-text: #3ab764;

/* 警告状态 */
--warning-bg: rgba(255, 119, 0, 0.1);
--warning-border: rgba(255, 119, 0, 0.2);
--warning-text: #FF7700;

/* 提升状态 */
--up-bg: rgba(0, 255, 230, 0.1);
--up-border: rgba(0, 255, 230, 0.2);
--up-text: rgba(0, 255, 230, 0.6);

/* 下降状态 */
--down-bg: rgba(255, 119, 0, 0.1);
--down-border: rgba(255, 119, 0, 0.2);
--down-text: rgba(255, 119, 0, 0.6);
```

## 字体系统 - Typography System

### 字体族
- **主字体**: 'Noto Sans SC', sans-serif
- **备用字体**: system-ui, -apple-system, sans-serif

### 字体大小层级
```css
--font-size-xs: 12px;          /* 时间戳、标签 */
--font-size-sm: 14px;          /* 正文、描述 */
--font-size-base: 16px;        /* 标题、按钮 */
--font-size-lg: 18px;          /* 大标题 */
--font-size-xl: 20px;          /* 特大标题 */
```

### 字体权重
- **Regular (400)**: 正文内容
- **Medium (500)**: 卡片标题、按钮
- **SemiBold (600)**: 数据展示、重要数值

### 行高系统
```css
--line-height-tight: 1.2;      /* 标题 */
--line-height-normal: 1.4;     /* 正文 */
--line-height-relaxed: 1.6;    /* 长文本 */
```

## 间距系统 - Spacing System

### 间距变量
```css
--spacing-xs: 4px;             /* 紧密间距 */
--spacing-sm: 8px;             /* 小间距 */
--spacing-md: 16px;            /* 标准间距 */
--spacing-lg: 24px;            /* 大间距 */
--spacing-xl: 32px;            /* 超大间距 */
--spacing-2xl: 48px;           /* 特大间距 */
```

### 间距使用规则
- **组件内部**: 使用 xs-sm 间距 (4px-8px)
- **组件之间**: 使用 md-lg 间距 (16px-24px)
- **页面边距**: 使用 lg-xl 间距 (24px-32px)
- **区块间距**: 使用 xl-2xl 间距 (32px-48px)

## 尺寸系统 - Size System

### 图标尺寸
```css
--icon-xs: 16px;               /* 小图标 */
--icon-sm: 20px;               /* 中小图标 */
--icon-md: 24px;               /* 标准图标 */
--icon-lg: 32px;               /* 大图标 */
--icon-xl: 48px;               /* 超大图标 */
```

### 按钮尺寸
```css
--button-height-sm: 32px;      /* 小按钮 */
--button-height-md: 40px;      /* 中按钮 */
--button-height-lg: 48px;      /* 大按钮 */
--button-height-xl: 56px;      /* 超大按钮 */
```

### 输入框尺寸
```css
--input-height: 56px;          /* 输入框高度 */
--input-width-full: 342px;     /* 全宽输入框 */
```

## 阴影系统 - Shadow System

### 阴影变量
```css
--shadow-card: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
--shadow-card-hover: 0px 16px 40px 0px rgba(6, 1, 43, 0.15);
--shadow-inner: inset 0 -12px 90px rgba(52, 43, 247, 0.1);
--shadow-button: 0px 4px 16px 0px rgba(114, 112, 255, 0.3);
```

### 阴影使用规则
- **卡片阴影**: 使用 `--shadow-card` 创建浮起效果
- **悬停阴影**: 使用 `--shadow-card-hover` 增强交互反馈
- **按钮阴影**: 使用 `--shadow-button` 突出可点击元素
- **内阴影**: 用于特殊背景效果

## 圆角系统 - Border Radius System

### 圆角变量
```css
--radius-xs: 4px;              /* 小元素 */
--radius-sm: 8px;              /* 按钮、标签 */
--radius-md: 16px;             /* 卡片、输入框 */
--radius-lg: 20px;             /* 头像、特殊元素 */
--radius-xl: 24px;             /* 大卡片 */
--radius-full: 9999px;         /* 圆形元素 */
```

### 圆角使用规则
- **小元素**: 4px (标签、小按钮)
- **标准元素**: 8px (按钮、导航项)
- **卡片容器**: 16px (卡片、输入框)
- **头像元素**: 20px (通知头像)
- **圆形元素**: 9999px (图标按钮)

## 动画系统 - Animation System

### 过渡时间
```css
--transition-fast: 0.15s;      /* 快速过渡 */
--transition-normal: 0.3s;     /* 标准过渡 */
--transition-slow: 0.5s;       /* 慢速过渡 */
```

### 缓动函数
```css
--ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
```

### 动画效果
- **悬停效果**: 向上移动 2-4px
- **缩放效果**: scale(1.05)
- **淡入动画**: opacity 0 → 1，translateY(20px) → 0
- **加载动画**: 0.6s 延迟淡入

## 背景系统 - Background System

### 渐变背景规范

背景由多个椭圆形渐变叠加组成，使用 SVG 滤镜实现模糊效果：

```css
/* 背景渐变颜色 */
--gradient-blue-1: #3234CA;    /* 椭圆 1，透明度 50%，模糊 300px */
--gradient-blue-2: #3D83F5;    /* 椭圆 2，透明度 25%，模糊 300px */
--gradient-purple: #9B38C6;    /* 椭圆 3，透明度 55%，模糊 150px */
--gradient-blue-3: #668CFF;    /* 椭圆 4，透明度 80%，模糊 150px */
--gradient-blue-4: #4848EF;    /* 椭圆 5，透明度 10%，内阴影效果 */
```

### 背景使用规则
- 基础背景色: `#eee` (浅色) / `#3a3a3a` (深色)
- 渐变椭圆使用绝对定位
- 椭圆层级低于内容层 (z-index < 10)
- 移动端需要调整椭圆大小和位置

## Z-Index 层级系统

```css
--z-background: -1;            /* 背景层 */
--z-base: 0;                   /* 基础层 */
--z-content: 10;               /* 内容层 */
--z-header: 20;                /* 头部层 */
--z-overlay: 30;               /* 遮罩层 */
--z-modal: 40;                 /* 模态框层 */
--z-tooltip: 50;               /* 提示层 */
```

## 断点系统 - Breakpoint System

### 响应式断点
```css
--breakpoint-sm: 640px;        /* 小屏幕 */
--breakpoint-md: 768px;        /* 中等屏幕 */
--breakpoint-lg: 1024px;       /* 大屏幕 */
--breakpoint-xl: 1280px;       /* 超大屏幕 */
--breakpoint-2xl: 1536px;      /* 特大屏幕 */
```

### 移动端适配规则
- 卡片宽度: 100%，最大宽度 400px
- 布局方向: 垂直排列
- 间距: 减少为 `var(--spacing-md)`
- 字体大小: 输入框使用 16px 防止 iOS 缩放
- 背景椭圆: 缩放和重新定位

## 主题系统 - Theme System

### 深色主题 (默认)
```css
[data-theme="dark"] {
  --bg-primary: var(--dark-bg);
  --bg-secondary: var(--dark-bg-alt);
  --text-primary: var(--white-text-1);
  --text-secondary: var(--white-text-2);
  --text-tertiary: var(--white-text-3);
}
```

### 浅色主题
```css
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: var(--dark-text-1);
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;
}
```

## 组件规范 - Component Specifications

### 按钮组件规范

**基础结构**:
```html
<button class="btn btn-primary btn-lg">
  按钮文字
</button>
```

**样式规范**:
- 主要按钮: 渐变背景 (#7270ff → #5a58ff)
- 次要按钮: 半透明背景，边框
- 高度: 32px (sm), 40px (md), 48px (lg), 56px (xl)
- 圆角: 16px
- 内边距: 8px-32px (根据尺寸)
- 悬停效果: 向上移动 2px，增强阴影

### 输入框组件规范

**基础结构**:
```html
<div class="input-group">
  <div class="input-icon">
    <svg><!-- 图标 --></svg>
  </div>
  <div class="input-divider"></div>
  <input type="text" class="input input-with-icon" placeholder="提示文字">
</div>
```

**样式规范**:
- 背景: rgba(0, 0, 0, 0.3)
- 边框: 1px solid rgba(255, 255, 255, 0.1)
- 圆角: 16px
- 高度: 56px
- 图标: 24x24px，透明度 60%
- 分隔线: 1px 宽，透明度 20%
- 焦点状态: 边框变为高亮色，外阴影

### 卡片组件规范

**基础结构**:
```html
<div class="card">
  <div class="card-header">
    <div class="icon icon-lg">
      <svg><!-- 图标 --></svg>
    </div>
    <h2 class="card-title">标题</h2>
    <div class="dropdown-trigger"><!-- 可选 --></div>
  </div>
  <div class="card-content">
    <!-- 内容 -->
  </div>
</div>
```

**样式规范**:
- 背景: #2d2d3f
- 圆角: 16px
- 阴影: 0px 12px 32px rgba(6, 1, 43, 0.1)
- 头部: 56px 高，半透明黑色背景
- 悬停效果: 向上移动 4px，增强阴影

### 图标组件规范

**尺寸规范**:
- xs: 16px (小图标)
- sm: 20px (中小图标)
- md: 24px (标准图标)
- lg: 32px (大图标)
- xl: 48px (超大图标)

**图标按钮**:
```html
<div class="icon-button icon-button-success">
  <svg width="24" height="24"><!-- 图标 --></svg>
</div>
```

**样式规范**:
- 尺寸: 48x48px 圆形
- 成功色: #3ab764
- 信息色: #3bb1b7
- 警告色: #FF7700
- 悬停效果: 缩放 1.05 倍

### 状态组件规范

**状态指示器**:
```html
<div class="status status-up">
  <svg width="16" height="16"><!-- 箭头图标 --></svg>
  <span>0.00%</span>
</div>
```

**状态类型**:
- `status-up`: 提升状态 (#00FFE6)
- `status-down`: 下降状态 (#FF7700)
- `status-success`: 成功状态 (#3ab764)
- `status-warning`: 警告状态 (#FF7700)
- `status-error`: 错误状态 (#d04747)
- `status-info`: 信息状态 (#3bb1b7)

### 导航组件规范

**导航项**:
```html
<div class="nav-item active">
  <div class="nav-icon">
    <svg width="24" height="24"><!-- 图标 --></svg>
  </div>
  <span class="nav-text">导航文字</span>
</div>
```

**状态规范**:
- 默认: 透明度 60% 白色文字
- 悬停: 半透明背景，透明度 90% 白色文字
- 选中: 渐变背景，白色边框，100% 白色文字

## 使用指南 - Usage Guidelines

### 创建新组件时

1. **遵循设计变量**: 使用 CSS 变量而非硬编码值
2. **保持一致性**: 使用统一的间距、圆角、阴影系统
3. **响应式设计**: 确保在不同屏幕尺寸下正常显示
4. **交互反馈**: 添加适当的悬停和焦点状态
5. **可访问性**: 确保足够的对比度和键盘导航支持

### 扩展设计系统时

1. **新增变量**: 遵循现有命名规范
2. **组件继承**: 基于现有组件扩展
3. **文档更新**: 及时更新组件文档
4. **测试验证**: 在不同环境下测试新组件

### AI 编程参考要点

1. **使用设计变量**: 所有颜色、尺寸、间距都使用 CSS 变量
2. **遵循组件结构**: 使用标准的 HTML 结构和 CSS 类名
3. **保持视觉一致性**: 新功能应与现有设计和谐统一
4. **响应式适配**: 确保移动端和桌面端都有良好体验
5. **性能优化**: 使用高效的 CSS 选择器和动画

### 代码示例模板

**新页面模板**:
```html
<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <link rel="stylesheet" href="../styles/components.css">
</head>
<body>
    <div class="container mx-auto min-h-screen p-md">
        <!-- 页面内容 -->
    </div>
</body>
</html>
```

**新组件模板**:
```css
.new-component {
  /* 使用设计变量 */
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal) var(--ease-out);
}

.new-component:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card-hover);
}
```

## 最佳实践 - Best Practices

### CSS 编写规范

1. **使用 CSS 变量**: 避免硬编码颜色和尺寸
2. **模块化组织**: 将相关样式组织在一起
3. **语义化命名**: 使用描述性的类名
4. **避免深层嵌套**: 保持 CSS 选择器简洁
5. **性能优化**: 使用高效的选择器

### HTML 结构规范

1. **语义化标签**: 使用合适的 HTML 标签
2. **可访问性**: 添加必要的 ARIA 属性
3. **结构清晰**: 保持 HTML 结构简洁明了
4. **类名一致**: 遵循统一的命名规范

### 响应式设计规范

1. **移动优先**: 从小屏幕开始设计
2. **渐进增强**: 逐步添加大屏幕特性
3. **触摸友好**: 确保触摸目标足够大
4. **性能考虑**: 优化移动端加载速度

这个设计系统为 AI 编程提供了完整的参考框架，确保生成的新界面与现有设计保持一致性和专业性。
