# 使用指南 - Usage Guide

## 概述

本指南详细说明如何使用这个 Figma 设计系统进行 AI 编程，包括文件结构、组件使用、最佳实践等。

## 项目结构 - Project Structure

```
figma-design-system/
├── pages/                    # 页面文件
│   ├── home.html            # 合作管理系统主页
│   └── login.html           # 登录页面
├── styles/                  # 样式文件
│   ├── variables.css        # 设计变量定义
│   ├── base.css            # 基础样式和工具类
│   └── components.css      # 组件样式
├── docs/                   # 文档
│   ├── design-system.md    # 设计系统规范
│   ├── components.md       # 组件库文档
│   └── usage-guide.md      # 使用指南 (本文档)
└── README.md              # 项目说明
```

## 快速开始 - Quick Start

### 1. 创建新页面

```html
<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新页面标题</title>
    <link rel="stylesheet" href="../styles/components.css">
</head>
<body>
    <div class="container mx-auto min-h-screen p-md">
        <!-- 页面内容 -->
    </div>
</body>
</html>
```

### 2. 使用设计变量

```css
.custom-component {
    background: var(--dark-bg);
    color: var(--white-text-1);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-card);
}
```

### 3. 应用组件类

```html
<!-- 按钮 -->
<button class="btn btn-primary btn-lg">主要按钮</button>

<!-- 输入框 -->
<div class="input-group">
    <input type="text" class="input" placeholder="请输入内容">
</div>

<!-- 卡片 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">卡片标题</h2>
    </div>
    <div class="card-content">
        卡片内容
    </div>
</div>
```

## AI 编程指南 - AI Programming Guide

### 提供给 AI 的上下文

当使用 AI 生成新界面时，请提供以下文档作为上下文：

1. **设计系统规范** (`docs/design-system.md`)
2. **组件库文档** (`docs/components.md`)
3. **现有页面代码** (`pages/` 目录下的文件)

### AI 提示词模板

```
请基于以下设计系统创建一个新的 [功能描述] 界面：

设计系统规范：
[粘贴 design-system.md 内容]

组件库：
[粘贴 components.md 内容]

要求：
1. 使用相同的设计变量和组件样式
2. 保持视觉一致性
3. 确保响应式设计
4. 遵循现有的 HTML 结构和 CSS 类名规范

请生成完整的 HTML 和 CSS 代码。
```

### 常用组件组合

#### 登录表单
```html
<div class="w-full max-w-md mx-auto">
    <div class="input-group mb-lg">
        <div class="input-icon">
            <!-- 用户图标 -->
        </div>
        <input type="text" class="input input-with-icon" placeholder="用户名">
    </div>
    <div class="input-group mb-xl">
        <div class="input-icon">
            <!-- 密码图标 -->
        </div>
        <input type="password" class="input input-with-icon" placeholder="密码">
    </div>
    <button class="btn btn-primary btn-lg btn-full">登录</button>
</div>
```

#### 数据展示卡片
```html
<div class="card">
    <div class="card-header">
        <div class="icon icon-lg">
            <!-- 图标 -->
        </div>
        <h2 class="card-title">数据标题</h2>
        <div class="dropdown-trigger">
            <span>本月</span>
            <!-- 下拉箭头 -->
        </div>
    </div>
    <div class="card-content p-0">
        <div class="stat-item">
            <span class="stat-label">指标1</span>
            <span class="stat-value">123</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">指标2</span>
            <span class="stat-value">456</span>
        </div>
    </div>
</div>
```

#### 通知列表
```html
<div class="card">
    <div class="card-header">
        <h2 class="card-title">通知</h2>
    </div>
    <div class="card-content">
        <div class="notification-item">
            <div class="notification-avatar">
                <!-- 头像图标 -->
            </div>
            <div class="notification-content">
                <div class="notification-text">通知内容</div>
                <div class="notification-time">时间</div>
            </div>
        </div>
    </div>
</div>
```

## 自定义指南 - Customization Guide

### 添加新颜色

1. 在 `styles/variables.css` 中添加新的颜色变量：

```css
:root {
    --custom-blue: #4285f4;
    --custom-blue-bg: rgba(66, 133, 244, 0.1);
    --custom-blue-border: rgba(66, 133, 244, 0.2);
}
```

2. 创建对应的状态类：

```css
.status-custom {
    background: var(--custom-blue-bg);
    border-color: var(--custom-blue-border);
    color: var(--custom-blue);
}
```

### 添加新组件

1. 在 `styles/components.css` 中添加组件样式：

```css
.new-component {
    /* 基础样式 */
    background: var(--dark-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    
    /* 交互效果 */
    transition: all var(--transition-normal) var(--ease-out);
}

.new-component:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}
```

2. 更新组件文档 (`docs/components.md`)

### 修改现有组件

1. 通过 CSS 变量修改：

```css
.btn-custom {
    --button-bg: linear-gradient(135deg, #ff6b6b, #ee5a24);
    --button-shadow: 0px 4px 16px rgba(255, 107, 107, 0.3);
}
```

2. 创建新的变体类：

```css
.card-compact {
    padding: var(--spacing-sm);
}

.card-compact .card-header {
    height: 40px;
    padding: var(--spacing-sm);
}
```

## 响应式设计 - Responsive Design

### 断点使用

```css
/* 移动端 */
@media (max-width: 768px) {
    .desktop-only {
        display: none;
    }
    
    .mobile-stack {
        flex-direction: column;
    }
}

/* 桌面端 */
@media (min-width: 769px) {
    .mobile-only {
        display: none;
    }
    
    .desktop-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}
```

### 移动端优化

1. **触摸目标**: 确保按钮至少 44px 高
2. **字体大小**: 输入框使用 16px 防止 iOS 缩放
3. **间距调整**: 移动端使用较小的间距
4. **布局简化**: 垂直堆叠复杂布局

## 性能优化 - Performance Optimization

### CSS 优化

1. **避免深层嵌套**：
```css
/* 好 */
.card-title { }

/* 避免 */
.card .card-header .card-title { }
```

2. **使用高效选择器**：
```css
/* 好 */
.btn { }

/* 避免 */
div.container > div.card > button { }
```

3. **合理使用动画**：
```css
/* 只对必要属性使用过渡 */
.btn {
    transition: transform var(--transition-fast) var(--ease-out);
}
```

### HTML 优化

1. **语义化标签**: 使用合适的 HTML 标签
2. **减少 DOM 层级**: 保持结构简洁
3. **懒加载**: 对非关键内容使用懒加载

## 可访问性 - Accessibility

### 基本要求

1. **颜色对比度**: 确保文字与背景有足够对比度
2. **键盘导航**: 所有交互元素支持键盘操作
3. **屏幕阅读器**: 添加适当的 ARIA 标签

### 实现示例

```html
<!-- 按钮 -->
<button class="btn btn-primary" aria-label="提交表单">
    提交
</button>

<!-- 输入框 -->
<div class="input-group">
    <label for="username" class="sr-only">用户名</label>
    <input id="username" type="text" class="input" 
           placeholder="请输入用户名" aria-required="true">
</div>

<!-- 状态指示器 -->
<div class="status status-success" role="status" aria-live="polite">
    <span aria-hidden="true">✓</span>
    <span>操作成功</span>
</div>
```

## 调试技巧 - Debugging Tips

### 常见问题

1. **样式不生效**: 检查 CSS 文件引入顺序
2. **变量未定义**: 确保 `variables.css` 正确加载
3. **响应式问题**: 使用浏览器开发者工具测试不同屏幕尺寸

### 调试工具

1. **浏览器开发者工具**: 检查元素样式
2. **CSS 变量检查**: 在控制台查看 `getComputedStyle()`
3. **响应式测试**: 使用设备模拟器

## 版本控制 - Version Control

### 文件组织

1. **样式文件**: 按功能分离 (变量、基础、组件)
2. **页面文件**: 按功能模块组织
3. **文档文件**: 保持同步更新

### 更新流程

1. 修改设计变量或组件
2. 更新相关文档
3. 测试现有页面兼容性
4. 提交完整的变更集

这个使用指南为开发者和 AI 提供了完整的使用框架，确保设计系统的正确应用和持续维护。
