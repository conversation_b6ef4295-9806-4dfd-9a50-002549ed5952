# 组件库文档 - Component Library

## 概述

本文档详细描述了从 Figma 设计中提取的所有 UI 组件，包括使用方法、变体和代码示例。

## 按钮组件 - Button Components

### 基础按钮

```html
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-secondary">次要按钮</button>
<button class="btn btn-ghost">幽灵按钮</button>
```

### 按钮尺寸

```html
<button class="btn btn-primary btn-sm">小按钮</button>
<button class="btn btn-primary btn-md">中按钮</button>
<button class="btn btn-primary btn-lg">大按钮</button>
<button class="btn btn-primary btn-xl">超大按钮</button>
```

### 按钮状态

```html
<button class="btn btn-primary">正常状态</button>
<button class="btn btn-primary" disabled>禁用状态</button>
<button class="btn btn-primary btn-full">全宽按钮</button>
```

### 设计规范
- **主要按钮**: 渐变背景 (#7270ff → #5a58ff)，白色文字
- **次要按钮**: 半透明背景，边框，白色文字
- **悬停效果**: 向上移动 2px，增强阴影
- **禁用状态**: 透明度 50%，禁用鼠标事件

## 输入框组件 - Input Components

### 基础输入框

```html
<div class="input-group">
    <input type="text" class="input" placeholder="请输入内容">
</div>
```

### 带图标的输入框

```html
<div class="input-group">
    <div class="input-icon">
        <svg><!-- 图标 SVG --></svg>
    </div>
    <div class="input-divider"></div>
    <input type="text" class="input input-with-icon" placeholder="请输入手机号">
</div>
```

### 带按钮的输入框

```html
<div class="input-group">
    <div class="input-icon">
        <svg><!-- 图标 SVG --></svg>
    </div>
    <div class="input-divider"></div>
    <input type="text" class="input input-with-icon" placeholder="请输入验证码">
    <button class="btn btn-secondary btn-sm">获取验证码</button>
</div>
```

### 设计规范
- **背景**: rgba(0, 0, 0, 0.3)
- **边框**: 1px solid rgba(255, 255, 255, 0.1)
- **圆角**: 16px
- **高度**: 56px
- **图标**: 24x24px，透明度 60%
- **分隔线**: 1px 宽，透明度 20%

## 卡片组件 - Card Components

### 基础卡片

```html
<div class="card">
    <div class="card-header">
        <div class="icon icon-lg">
            <svg><!-- 图标 SVG --></svg>
        </div>
        <h2 class="card-title">卡片标题</h2>
    </div>
    <div class="card-content">
        <!-- 卡片内容 -->
    </div>
</div>
```

### 带下拉菜单的卡片

```html
<div class="card">
    <div class="card-header">
        <div class="icon icon-lg">
            <svg><!-- 图标 SVG --></svg>
        </div>
        <h2 class="card-title">卡片标题</h2>
        <div class="dropdown-trigger">
            <span>本月</span>
            <svg><!-- 下拉箭头 --></svg>
        </div>
    </div>
    <div class="card-content">
        <!-- 卡片内容 -->
    </div>
</div>
```

### 设计规范
- **背景**: #2d2d3f
- **圆角**: 16px
- **阴影**: 0px 12px 32px rgba(6, 1, 43, 0.1)
- **头部**: 56px 高，半透明黑色背景
- **悬停效果**: 向上移动 4px，增强阴影

## 图标组件 - Icon Components

### 基础图标

```html
<div class="icon icon-xs"><!-- 16px --></div>
<div class="icon icon-sm"><!-- 20px --></div>
<div class="icon icon-md"><!-- 24px --></div>
<div class="icon icon-lg"><!-- 32px --></div>
<div class="icon icon-xl"><!-- 48px --></div>
```

### 图标按钮

```html
<div class="icon-button icon-button-success">
    <svg width="24" height="24"><!-- 图标 SVG --></svg>
</div>
<div class="icon-button icon-button-info">
    <svg width="24" height="24"><!-- 图标 SVG --></svg>
</div>
<div class="icon-button icon-button-warning">
    <svg width="24" height="24"><!-- 图标 SVG --></svg>
</div>
```

### 设计规范
- **成功色**: #3ab764 (绿色)
- **信息色**: #3bb1b7 (蓝绿色)
- **警告色**: #FF7700 (橙色)
- **尺寸**: 48x48px 圆形背景
- **悬停效果**: 缩放 1.05 倍

## 状态组件 - Status Components

### 状态指示器

```html
<div class="status status-up">
    <svg width="16" height="16"><!-- 向上箭头 --></svg>
    <span>0.00%</span>
</div>
<div class="status status-down">
    <svg width="16" height="16"><!-- 向下箭头 --></svg>
    <span>0.00%</span>
</div>
```

### 其他状态

```html
<div class="status status-success">成功状态</div>
<div class="status status-warning">警告状态</div>
<div class="status status-error">错误状态</div>
<div class="status status-info">信息状态</div>
```

### 设计规范
- **提升状态**: #00FFE6 (青色)，向上箭头
- **下降状态**: #FF7700 (橙色)，向下箭头
- **背景**: 半透明色彩背景
- **边框**: 同色系边框
- **圆角**: 16px

## 导航组件 - Navigation Components

### 导航项

```html
<div class="nav-item">
    <div class="nav-icon">
        <svg width="24" height="24"><!-- 图标 SVG --></svg>
    </div>
    <span class="nav-text">导航文字</span>
</div>
```

### 选中状态

```html
<div class="nav-item active">
    <div class="nav-icon">
        <svg width="24" height="24"><!-- 图标 SVG --></svg>
    </div>
    <span class="nav-text">仪表盘</span>
</div>
```

### 设计规范
- **默认状态**: 透明度 60% 的白色文字
- **悬停状态**: 半透明背景，透明度 90% 的白色文字
- **选中状态**: 渐变背景，白色边框，100% 白色文字
- **圆角**: 8px
- **内边距**: 8px 16px

## 通知组件 - Notification Components

### 通知项

```html
<div class="notification-item">
    <div class="notification-avatar">
        <div class="icon icon-sm">
            <svg width="20" height="20"><!-- 图标 SVG --></svg>
        </div>
    </div>
    <div class="notification-content">
        <div class="notification-text">通知内容</div>
        <div class="notification-time">时间信息</div>
    </div>
</div>
```

### 设计规范
- **头像**: 40x40px 圆形，半透明白色背景
- **文字**: 主要文字 90% 透明度，时间 60% 透明度
- **悬停效果**: 半透明背景高亮

## 统计组件 - Stats Components

### 统计项

```html
<div class="stat-item">
    <span class="stat-label">标签</span>
    <span class="stat-value">数值</span>
</div>
```

### 设计规范
- **布局**: 左右对齐
- **内边距**: 16px 24px
- **边框**: 底部 1px 分隔线
- **标签**: 14px，90% 透明度
- **数值**: 14px，600 字重，100% 白色

## Logo 组件 - Logo Components

### ICL Market Logo

```html
<div class="logo">
    <div class="logo-icon">
        <svg width="74" height="24">
            <!-- 复杂的 SVG 路径 -->
        </svg>
    </div>
</div>
```

### 设计规范
- **尺寸**: 74x24px
- **颜色**: #FAF9FF (ICL 图标)，白色 (文字)
- **字体**: 自定义 SVG 路径
- **用途**: 品牌标识，登录页面

## 使用指南

### 组合使用示例

```html
<!-- 登录表单组合 -->
<div class="w-full max-w-md">
    <div class="input-group mb-lg">
        <!-- 手机号输入框 -->
    </div>
    <div class="input-group mb-xl">
        <!-- 验证码输入框 -->
    </div>
    <button class="btn btn-primary btn-lg btn-full">
        登录
    </button>
</div>
```

### 响应式适配

所有组件都支持响应式设计：

```css
@media (max-width: 768px) {
    .card {
        width: 100% !important;
        max-width: 400px;
    }
    
    .btn-full {
        width: 100%;
    }
}
```

### 主题支持

组件支持深色和浅色主题：

```html
<html data-theme="dark">  <!-- 深色主题 -->
<html data-theme="light"> <!-- 浅色主题 -->
```

### 自定义变体

可以通过 CSS 变量自定义组件样式：

```css
.custom-button {
    --button-bg: linear-gradient(135deg, #ff6b6b, #ee5a24);
    --button-shadow: 0px 4px 16px rgba(255, 107, 107, 0.3);
}
```
