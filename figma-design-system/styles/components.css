/* ==========================================================================
   组件样式 - Component Styles
   ========================================================================== */

/* 导入基础样式 */
@import url('./base.css');

/* ==========================================================================
   按钮组件 - Button Components
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast) var(--ease-out);
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-sm {
  height: var(--button-height-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.btn-md {
  height: var(--button-height-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  height: var(--button-height-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
}

.btn-xl {
  height: var(--button-height-xl);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, var(--text-highlight) 0%, #5a58ff 100%);
  color: white;
  box-shadow: var(--shadow-button);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 24px 0px rgba(114, 112, 255, 0.4);
}

.btn-secondary {
  background: var(--input-bg);
  color: var(--white-text-1);
  border: 1px solid var(--border-input);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-ghost {
  background: transparent;
  color: var(--white-text-1);
  border: 1px solid var(--border-input);
}

.btn-ghost:hover {
  background: var(--input-bg);
}

.btn-full {
  width: 100%;
}

/* ==========================================================================
   输入框组件 - Input Components
   ========================================================================== */

.input-group {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
  height: var(--input-height);
  padding: var(--spacing-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  color: var(--white-text-1);
  background: var(--input-bg);
  border: 1px solid var(--border-input);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) var(--ease-out);
}

.input::placeholder {
  color: var(--white-text-3);
}

.input:focus {
  border-color: var(--text-highlight);
  box-shadow: 0 0 0 3px rgba(114, 112, 255, 0.1);
}

.input-with-icon {
  padding-left: calc(var(--icon-md) + var(--spacing-lg));
}

.input-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  width: var(--icon-md);
  height: var(--icon-md);
  color: var(--white-text-2);
  pointer-events: none;
}

.input-divider {
  position: absolute;
  left: calc(var(--icon-md) + var(--spacing-lg));
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background: var(--white-text-3);
  opacity: 0.2;
}

/* ==========================================================================
   卡片组件 - Card Components
   ========================================================================== */

.card {
  background: var(--dark-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  transition: all var(--transition-normal) var(--ease-out);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
}

.card-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid var(--dark-line-1);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--white-text-1);
  flex: 1;
}

.card-content {
  padding: var(--spacing-md);
}

.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--dark-line-1);
}

/* ==========================================================================
   图标组件 - Icon Components
   ========================================================================== */

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-xs { width: var(--icon-xs); height: var(--icon-xs); }
.icon-sm { width: var(--icon-sm); height: var(--icon-sm); }
.icon-md { width: var(--icon-md); height: var(--icon-md); }
.icon-lg { width: var(--icon-lg); height: var(--icon-lg); }
.icon-xl { width: var(--icon-xl); height: var(--icon-xl); }

.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-xl);
  height: var(--icon-xl);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast) var(--ease-out);
}

.icon-button:hover {
  transform: scale(1.05);
}

.icon-button-success {
  background: var(--success-green);
}

.icon-button-info {
  background: var(--info-blue);
}

.icon-button-warning {
  background: var(--warning-orange);
}

/* ==========================================================================
   状态组件 - Status Components
   ========================================================================== */

.status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid transparent;
}

.status-up {
  background: var(--up-bg);
  border-color: var(--up-border);
  color: var(--up-text);
}

.status-down {
  background: var(--down-bg);
  border-color: var(--down-border);
  color: var(--down-text);
}

.status-success {
  background: var(--success-bg);
  border-color: var(--success-border);
  color: var(--success-text);
}

.status-warning {
  background: var(--warning-bg);
  border-color: var(--warning-border);
  color: var(--warning-text);
}

.status-error {
  background: var(--error-bg);
  border-color: var(--error-border);
  color: var(--error-text);
}

.status-info {
  background: var(--info-bg);
  border-color: var(--info-border);
  color: var(--info-text);
}

/* ==========================================================================
   导航组件 - Navigation Components
   ========================================================================== */

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  color: var(--white-text-2);
  text-decoration: none;
  transition: all var(--transition-fast) var(--ease-out);
  cursor: pointer;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--white-text-1);
}

.nav-item.active {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: var(--white-text-1);
}

.nav-icon {
  width: var(--icon-md);
  height: var(--icon-md);
  flex-shrink: 0;
}

.nav-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
}

/* ==========================================================================
   通知组件 - Notification Components
   ========================================================================== */

.notification-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast) var(--ease-out);
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.02);
}

.notification-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.notification-text {
  font-size: var(--font-size-sm);
  color: var(--white-text-1);
  line-height: var(--line-height-normal);
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--white-text-2);
}

/* ==========================================================================
   统计组件 - Stats Components
   ========================================================================== */

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--dark-line-1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--white-text-1);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: #ffffff;
}

/* ==========================================================================
   Logo 组件 - Logo Components
   ========================================================================== */

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  width: 74px;
  height: 24px;
  flex-shrink: 0;
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--white-text-1);
}

/* ==========================================================================
   下拉菜单组件 - Dropdown Components
   ========================================================================== */

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--white-text-1);
  cursor: pointer;
  transition: all var(--transition-fast) var(--ease-out);
}

.dropdown-trigger:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* ==========================================================================
   响应式组件样式 - Responsive Component Styles
   ========================================================================== */

@media (max-width: 768px) {
  .card {
    margin: var(--spacing-sm);
  }
  
  .btn-full {
    width: 100%;
  }
  
  .input {
    font-size: 16px; /* 防止 iOS 缩放 */
  }
  
  .nav-item {
    padding: var(--spacing-md);
  }
}
