/* ==========================================================================
   基础样式 - Base Styles
   ========================================================================== */

/* 导入设计变量 */
@import url('./variables.css');

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap');

/* ==========================================================================
   CSS 重置 - CSS Reset
   ========================================================================== */

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary), var(--font-family-fallback);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary, var(--white-text-1));
  background-color: var(--bg-primary, var(--dark-bg));
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  min-height: 100vh;
}

/* ==========================================================================
   基础元素样式 - Base Element Styles
   ========================================================================== */

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
}

h1 { font-size: var(--font-size-xl); }
h2 { font-size: var(--font-size-lg); }
h3 { font-size: var(--font-size-base); }
h4 { font-size: var(--font-size-sm); }
h5 { font-size: var(--font-size-xs); }
h6 { font-size: var(--font-size-xs); }

/* 段落 */
p {
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-normal);
}

/* 链接 */
a {
  color: var(--text-highlight);
  text-decoration: none;
  transition: color var(--transition-fast) var(--ease-out);
}

a:hover {
  color: var(--text-highlight);
  opacity: 0.8;
}

/* 按钮重置 */
button {
  font-family: inherit;
  font-size: inherit;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
}

/* 输入框重置 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  border: none;
  background: none;
  outline: none;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* SVG */
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ==========================================================================
   布局工具类 - Layout Utilities
   ========================================================================== */

/* 容器 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Flexbox 工具 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* Grid 工具 */
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* ==========================================================================
   定位工具类 - Position Utilities
   ========================================================================== */

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* ==========================================================================
   尺寸工具类 - Size Utilities
   ========================================================================== */

.w-full { width: 100%; }
.h-full { height: 100%; }
.size-full { width: 100%; height: 100%; }

.w-auto { width: auto; }
.h-auto { height: auto; }

.min-h-screen { min-height: 100vh; }
.min-h-full { min-height: 100%; }

/* ==========================================================================
   间距工具类 - Spacing Utilities
   ========================================================================== */

/* 内边距 */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }

.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }

/* 外边距 */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mx-auto { margin-left: auto; margin-right: auto; }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

/* ==========================================================================
   文本工具类 - Text Utilities
   ========================================================================== */

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary, var(--white-text-1)); }
.text-secondary { color: var(--text-secondary, var(--white-text-2)); }
.text-tertiary { color: var(--text-tertiary, var(--white-text-3)); }

.text-nowrap { white-space: nowrap; }
.text-wrap { white-space: normal; }

/* ==========================================================================
   背景工具类 - Background Utilities
   ========================================================================== */

.bg-primary { background-color: var(--bg-primary, var(--dark-bg)); }
.bg-secondary { background-color: var(--bg-secondary, var(--dark-bg-alt)); }
.bg-transparent { background-color: transparent; }

/* ==========================================================================
   边框工具类 - Border Utilities
   ========================================================================== */

.border { border: 1px solid var(--border-primary, var(--dark-line-1)); }
.border-0 { border: none; }

.border-primary { border-color: var(--border-primary, var(--dark-line-1)); }
.border-secondary { border-color: var(--border-secondary, var(--dark-line-2)); }

.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ==========================================================================
   阴影工具类 - Shadow Utilities
   ========================================================================== */

.shadow-none { box-shadow: none; }
.shadow-card { box-shadow: var(--shadow-card); }
.shadow-card-hover { box-shadow: var(--shadow-card-hover); }
.shadow-button { box-shadow: var(--shadow-button); }

/* ==========================================================================
   显示工具类 - Display Utilities
   ========================================================================== */

.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* ==========================================================================
   溢出工具类 - Overflow Utilities
   ========================================================================== */

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* ==========================================================================
   过渡动画工具类 - Transition Utilities
   ========================================================================== */

.transition {
  transition: all var(--transition-normal) var(--ease-out);
}

.transition-fast {
  transition: all var(--transition-fast) var(--ease-out);
}

.transition-slow {
  transition: all var(--transition-slow) var(--ease-out);
}

/* ==========================================================================
   响应式工具类 - Responsive Utilities
   ========================================================================== */

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .md\:hidden {
    display: none;
  }
  
  .md\:flex-col {
    flex-direction: column;
  }
  
  .md\:text-center {
    text-align: center;
  }
}

@media (min-width: 769px) {
  .md\:block {
    display: block;
  }
  
  .md\:flex {
    display: flex;
  }
  
  .md\:grid {
    display: grid;
  }
}
