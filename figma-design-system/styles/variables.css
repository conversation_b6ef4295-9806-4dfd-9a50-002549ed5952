/* ==========================================================================
   设计系统变量 - Design System Variables
   ========================================================================== */

:root {
  /* ==========================================================================
     颜色系统 - Color System
     ========================================================================== */
  
  /* 文本颜色 - Text Colors */
  --white-text-1: #ffffffe5;     /* 主要文本，透明度 90% */
  --white-text-2: #ffffff99;     /* 次要文本，透明度 60% */
  --white-text-3: #ffffff4d;     /* 辅助文本，透明度 30% */
  --dark-text-1: #000000d9;      /* 深色文本，透明度 85% */
  
  /* 背景颜色 - Background Colors */
  --dark-bg: #2d2d3f;            /* 主要深色背景 */
  --dark-bg-alt: #3a3a3a;        /* 替代深色背景 */
  --input-bg: rgba(0, 0, 0, 0.3); /* 输入框背景 */
  --card-bg: rgba(10, 2, 41, 0.1); /* 卡片背景 */
  
  /* 主题颜色 - Theme Colors */
  --text-highlight: #7270ff;     /* 高亮颜色 */
  --red: #d04747;                 /* 错误/警告红色 */
  --purple: #9B38C6;              /* 紫色主题 */
  
  /* 功能颜色 - Functional Colors */
  --success-green: #3ab764;       /* 成功绿色 */
  --info-blue: #3bb1b7;          /* 信息蓝色 */
  --warning-orange: #FF7700;      /* 警告橙色 */
  --accent-cyan: #00FFE6;         /* 强调青色 */
  
  /* 边框颜色 - Border Colors */
  --dark-line-1: #ffffff1a;       /* 浅边框，透明度 10% */
  --dark-line-2: #ffffff26;       /* 深边框，透明度 15% */
  --border-input: rgba(255, 255, 255, 0.1); /* 输入框边框 */
  
  /* 背景渐变颜色 - Gradient Colors */
  --gradient-blue-1: #3234CA;
  --gradient-blue-2: #3D83F5;
  --gradient-purple: #9B38C6;
  --gradient-blue-3: #668CFF;
  --gradient-blue-4: #4848EF;
  
  /* ==========================================================================
     字体系统 - Typography System
     ========================================================================== */
  
  /* 字体族 - Font Families */
  --font-family-primary: 'Noto Sans SC', sans-serif;
  --font-family-fallback: system-ui, -apple-system, sans-serif;
  
  /* 字体大小 - Font Sizes */
  --font-size-xs: 12px;          /* 时间戳、标签 */
  --font-size-sm: 14px;          /* 正文、描述 */
  --font-size-base: 16px;        /* 标题、按钮 */
  --font-size-lg: 18px;          /* 大标题 */
  --font-size-xl: 20px;          /* 特大标题 */
  
  /* 字体权重 - Font Weights */
  --font-weight-normal: 400;     /* 正文内容 */
  --font-weight-medium: 500;     /* 卡片标题、按钮 */
  --font-weight-semibold: 600;   /* 数据展示、重要数值 */
  
  /* 行高 - Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* ==========================================================================
     间距系统 - Spacing System
     ========================================================================== */
  
  --spacing-xs: 4px;             /* 紧密间距 */
  --spacing-sm: 8px;             /* 小间距 */
  --spacing-md: 16px;            /* 标准间距 */
  --spacing-lg: 24px;            /* 大间距 */
  --spacing-xl: 32px;            /* 超大间距 */
  --spacing-2xl: 48px;           /* 特大间距 */
  
  /* ==========================================================================
     尺寸系统 - Size System
     ========================================================================== */
  
  /* 图标尺寸 - Icon Sizes */
  --icon-xs: 16px;
  --icon-sm: 20px;
  --icon-md: 24px;
  --icon-lg: 32px;
  --icon-xl: 48px;
  
  /* 按钮尺寸 - Button Sizes */
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  --button-height-xl: 56px;
  
  /* 输入框尺寸 - Input Sizes */
  --input-height: 56px;
  --input-width-full: 342px;
  
  /* ==========================================================================
     阴影系统 - Shadow System
     ========================================================================== */
  
  --shadow-card: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
  --shadow-card-hover: 0px 16px 40px 0px rgba(6, 1, 43, 0.15);
  --shadow-inner: inset 0 -12px 90px rgba(52, 43, 247, 0.1);
  --shadow-button: 0px 4px 16px 0px rgba(114, 112, 255, 0.3);
  
  /* ==========================================================================
     圆角系统 - Border Radius System
     ========================================================================== */
  
  --radius-xs: 4px;              /* 小元素 */
  --radius-sm: 8px;              /* 按钮、标签 */
  --radius-md: 16px;             /* 卡片、输入框 */
  --radius-lg: 20px;             /* 头像、特殊元素 */
  --radius-xl: 24px;             /* 大卡片 */
  --radius-full: 9999px;         /* 圆形元素 */
  
  /* ==========================================================================
     动画系统 - Animation System
     ========================================================================== */
  
  /* 过渡时间 - Transition Durations */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 缓动函数 - Easing Functions */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* ==========================================================================
     Z-Index 层级系统 - Z-Index System
     ========================================================================== */
  
  --z-background: -1;
  --z-base: 0;
  --z-content: 10;
  --z-header: 20;
  --z-overlay: 30;
  --z-modal: 40;
  --z-tooltip: 50;
  
  /* ==========================================================================
     断点系统 - Breakpoint System
     ========================================================================== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ==========================================================================
   深色主题变量 - Dark Theme Variables
   ========================================================================== */

[data-theme="dark"] {
  --bg-primary: var(--dark-bg);
  --bg-secondary: var(--dark-bg-alt);
  --text-primary: var(--white-text-1);
  --text-secondary: var(--white-text-2);
  --text-tertiary: var(--white-text-3);
  --border-primary: var(--dark-line-1);
  --border-secondary: var(--dark-line-2);
}

/* ==========================================================================
   浅色主题变量 - Light Theme Variables
   ========================================================================== */

[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: var(--dark-text-1);
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;
  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
}

/* ==========================================================================
   状态颜色变量 - State Color Variables
   ========================================================================== */

:root {
  /* 成功状态 */
  --success-bg: rgba(58, 183, 100, 0.1);
  --success-border: rgba(58, 183, 100, 0.2);
  --success-text: #3ab764;
  
  /* 警告状态 */
  --warning-bg: rgba(255, 119, 0, 0.1);
  --warning-border: rgba(255, 119, 0, 0.2);
  --warning-text: #FF7700;
  
  /* 错误状态 */
  --error-bg: rgba(208, 71, 71, 0.1);
  --error-border: rgba(208, 71, 71, 0.2);
  --error-text: #d04747;
  
  /* 信息状态 */
  --info-bg: rgba(59, 177, 183, 0.1);
  --info-border: rgba(59, 177, 183, 0.2);
  --info-text: #3bb1b7;
  
  /* 提升状态 */
  --up-bg: rgba(0, 255, 230, 0.1);
  --up-border: rgba(0, 255, 230, 0.2);
  --up-text: rgba(0, 255, 230, 0.6);
  
  /* 下降状态 */
  --down-bg: rgba(255, 119, 0, 0.1);
  --down-border: rgba(255, 119, 0, 0.2);
  --down-text: rgba(255, 119, 0, 0.6);
}
