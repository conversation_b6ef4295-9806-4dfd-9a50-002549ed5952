<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - ICL Market</title>
    <link rel="stylesheet" href="../styles/components.css">
</head>
<body class="bg-secondary">
    <div class="container mx-auto min-h-screen flex flex-col items-center justify-center p-md">
        <!-- Logo 区域 -->
        <div class="logo mb-xl">
            <div class="logo-icon">
                <!-- ICL MARKET Logo -->
                <svg width="74" height="24" viewBox="0 0 74 24" fill="none">
                    <!-- ICL 图标 -->
                    <g>
                        <path d="M14.5332 18.6855H8.71973V9.96582H14.5332V18.6855ZM0 9.96582V0H11.626L0 9.96582ZM23.2529 9.96582L11.626 0H23.2529V9.96582Z" fill="#FAF9FF"/>
                    </g>
                    <!-- MARKET 文字 -->
                    <g transform="translate(29, 15)">
                        <path d="M3.90527 2.625L5.88379 0H7.80566V7.20117H5.85156V2.54688L3.84375 5.19336L1.85742 2.54785V7.20117H0V0H1.97559L3.90527 2.625ZM16.1006 7.20117H14.0928L13.6279 5.78711H11.0518L10.5947 7.20117H8.78125L11.4697 0H13.6182L16.1006 7.20117ZM20.7012 0C21.5359 0 22.1946 0.190959 22.6768 0.572266C23.159 0.953722 23.4004 1.53678 23.4004 2.32129C23.4004 2.61636 23.3391 2.91551 23.2168 3.21777C23.1016 3.51986 22.9213 3.78641 22.6768 4.0166C22.4808 4.20057 22.2402 4.3339 21.9561 4.41992L23.7891 7.20117H21.2842L19.9463 4.81543H19.0283V7.20117H17.0742V0H20.7012ZM26.9805 2.78418L28.9238 0H31.3848L28.7471 2.97266L31.5898 7.20117H29.1611L27.4043 4.48633L26.9805 4.96484V7.20117H25.0156V0H26.9805V2.78418ZM37.6396 1.49023H34.584V2.85059H37.2188V4.33984H34.5732V5.71094H37.6396V7.20117H32.6191V0H37.6396V1.49023ZM44.4922 1.49023H42.5381V7.20117H40.5947V1.49023H38.6729V0H44.4922V1.49023ZM11.501 4.39453H13.1699L12.3281 1.83301L11.501 4.39453ZM19.0283 3.41113H20.4209C20.6656 3.41112 20.8706 3.3357 21.0361 3.18457C21.2016 3.03341 21.2842 2.77775 21.2842 2.41797C21.2841 2.1015 21.2051 1.86787 21.0469 1.7168C20.8957 1.56565 20.7014 1.49023 20.4639 1.49023H19.0283V3.41113Z" fill="white"/>
                    </g>
                    <!-- TUNING 文字 -->
                    <g transform="translate(29, 3)">
                        <path d="M8.95996 4.63184C8.95999 5.14273 9.05668 5.52095 9.25098 5.76562C9.45248 6.01019 9.78018 6.1318 10.2334 6.13184C10.6363 6.13184 10.9497 6.01017 11.1729 5.76562C11.3959 5.52095 11.5078 5.14273 11.5078 4.63184V0.172852H13.3643V4.73926C13.3643 5.40861 13.2347 5.95622 12.9756 6.38086C12.7166 6.80521 12.3568 7.11785 11.8965 7.31934C11.4431 7.52083 10.9211 7.62204 10.3311 7.62207C9.18669 7.62207 8.3303 7.39129 7.76172 6.93066C7.20033 6.47004 6.91895 5.73968 6.91895 4.73926V0.172852H8.95996V4.63184ZM37.5898 0C38.0576 0 38.5112 0.0470674 38.9502 0.140625C39.3964 0.23419 39.8251 0.363759 40.2354 0.529297V2.28906C39.7819 2.04435 39.3674 1.85355 38.9932 1.7168C38.6262 1.57291 38.2444 1.50098 37.8486 1.50098C37.4747 1.50103 37.1512 1.56947 36.8779 1.70605C36.6117 1.83559 36.4057 2.06549 36.2617 2.39648C36.1178 2.7203 36.0459 3.17756 36.0459 3.76758C36.0459 4.34312 36.1214 4.80044 36.2725 5.13867C36.4236 5.46975 36.626 5.70407 36.8779 5.84082C37.1369 5.97743 37.4247 6.0459 37.7412 6.0459C37.9209 6.0459 38.0827 6.03522 38.2266 6.01367C38.3776 5.9849 38.5038 5.94487 38.6045 5.89453V4.7832H37.5254V3.29297H40.5586V6.7041C40.0981 6.97035 39.616 7.1754 39.1123 7.31934C38.6157 7.45608 38.1116 7.52539 37.6006 7.52539C36.8955 7.52537 36.2766 7.40278 35.7441 7.1582C35.2116 6.91352 34.7935 6.51761 34.4912 5.9707C34.1961 5.41651 34.0488 4.68518 34.0488 3.77832C34.0489 2.89323 34.1962 2.17325 34.4912 1.61914C34.7935 1.05783 35.2116 0.647751 35.7441 0.388672C36.2766 0.129669 36.8919 1.8175e-05 37.5898 0ZM5.81934 1.66309H3.86523V7.37402H1.92188V1.66309H0V0.172852H5.81934V1.66309ZM18.5723 0.172852C19.4071 0.172852 20.0656 0.36372 20.5479 0.745117C21.0301 1.12657 21.2715 1.70964 21.2715 2.49414C21.2715 2.78907 21.2101 3.08753 21.0879 3.38965C20.9727 3.69193 20.7926 3.95914 20.5479 4.18945C20.3517 4.37362 20.1107 4.50675 19.8262 4.59277L21.6602 7.37402H19.1553L17.8174 4.98828H16.8994V7.37402H14.9453V0.172852H18.5723ZM24.8516 7.37402H22.8867V0.172852H24.8516V7.37402ZM30.998 4.08105V0.172852H32.79V7.37402H31.3652L28.2129 3.32227V7.37402H26.4316V0.172852H28.0508L30.998 4.08105ZM16.8994 3.58398H18.292C18.5366 3.58393 18.7418 3.50851 18.9072 3.35742C19.0726 3.20625 19.1553 2.95053 19.1553 2.59082C19.1552 2.27442 19.0762 2.04074 18.918 1.88965C18.7668 1.73852 18.5724 1.6631 18.335 1.66309H16.8994V3.58398Z" fill="white"/>
                    </g>
                </svg>
            </div>
        </div>

        <!-- 登录表单 -->
        <div class="w-full max-w-md">
            <!-- 手机号输入框 -->
            <div class="input-group mb-lg">
                <div class="input-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M10.75 0C12.8216 0 14.5 1.68141 14.5 3.75195V17.748C14.5 19.8186 12.8216 21.5 10.75 21.5H3.75C1.67893 21.5 0 19.8211 0 17.75V3.75C0 1.67893 1.67893 8.0532e-09 3.75 0H10.75ZM3.75 1.5C2.50736 1.5 1.5 2.50736 1.5 3.75V17.75C1.5 18.9926 2.50736 20 3.75 20H10.75C11.9921 20 13 18.9912 13 17.748V3.75195C13 2.50879 11.9921 1.5 10.75 1.5H3.75ZM8.75 3C9.16421 3 9.5 3.33579 9.5 3.75C9.5 4.16421 9.16421 4.5 8.75 4.5H5.75C5.33579 4.5 5 4.16421 5 3.75C5 3.33579 5.33579 3 5.75 3H8.75Z" fill="white" opacity="0.6"/>
                    </svg>
                </div>
                <div class="input-divider"></div>
                <input type="tel" class="input input-with-icon" placeholder="请输入手机号" style="padding-left: 72px;">
            </div>

            <!-- 验证码输入框 -->
            <div class="input-group mb-xl">
                <div class="input-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4.87793 0.90237C6.96466 -0.30079 9.53534 -0.30079 11.6221 0.90237L15.124 2.9219C15.9752 3.41296 16.4999 4.32102 16.5 5.30374V11.8907C16.4998 14.6273 15.0562 17.1611 12.7021 18.5567L10.1621 20.0625C8.9832 20.7613 7.5168 20.7613 6.33789 20.0625L3.79785 18.5567C1.44382 17.1611 0.000228139 14.6273 0 11.8907V5.30374C0.000108966 4.32102 0.52476 3.41296 1.37598 2.9219L4.87793 0.90237ZM10.8721 2.20218C9.24912 1.26648 7.25088 1.26648 5.62793 2.20218L2.12598 4.22073C1.73905 4.4439 1.50011 4.85707 1.5 5.30374V11.8907C1.50023 14.0975 2.6642 16.1412 4.5625 17.2666L7.10254 18.7725C7.80987 19.1917 8.69013 19.1917 9.39746 18.7725L11.9375 17.2666C13.8358 16.1412 14.9998 14.0975 15 11.8907V5.30374C14.9999 4.85707 14.761 4.4439 14.374 4.22073L10.8721 2.20218ZM11.2197 7.29397C11.5126 7.00108 11.9874 7.00108 12.2803 7.29397C12.573 7.58688 12.5731 8.06168 12.2803 8.35452L7.78027 12.8545C7.48742 13.1472 7.01258 13.1472 6.71973 12.8545L4.21973 10.3545C3.92689 10.0617 3.92701 9.58688 4.21973 9.29397C4.49433 9.01937 4.92905 9.00191 5.22363 9.24221L5.28027 9.29397L7.25 11.2637L11.2197 7.29397Z" fill="white" opacity="0.6"/>
                    </svg>
                </div>
                <div class="input-divider"></div>
                <input type="text" class="input input-with-icon" placeholder="请输入验证码" style="padding-left: 72px; padding-right: 120px;">
                <button class="btn btn-secondary btn-sm absolute right-2 top-1/2 transform -translate-y-1/2">
                    获取验证码
                </button>
            </div>

            <!-- 登录按钮 -->
            <button class="btn btn-primary btn-lg btn-full mb-lg">
                登录
            </button>

            <!-- 功能图标 -->
            <div class="flex justify-center gap-md">
                <div class="icon-button icon-button-info">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22.5 8.27156C22.4624 9.57718 21.9255 10.8187 21 11.7403L17.7422 15C17.2677 15.4771 16.7033 15.8553 16.0817 16.1128C15.46 16.3703 14.7935 16.5019 14.1206 16.5H14.1159C13.4316 16.4995 12.7543 16.3619 12.124 16.0952C11.4938 15.8286 10.9233 15.4384 10.4464 14.9476C9.96953 14.4568 9.59583 13.8754 9.34739 13.2377C9.09896 12.6001 8.98083 11.9191 9 11.235C9.00559 11.0361 9.08998 10.8475 9.23458 10.7108C9.37919 10.5742 9.57218 10.5005 9.77109 10.5061C9.97001 10.5117 10.1585 10.5961 10.2952 10.7407C10.4319 10.8853 10.5056 11.0783 10.5 11.2772C10.4863 11.7609 10.5698 12.2424 10.7454 12.6933C10.921 13.1442 11.1852 13.5553 11.5223 13.9023C11.8595 14.2494 12.2628 14.5253 12.7085 14.7139C13.1541 14.9024 13.633 14.9997 14.1169 15C14.5925 15.0012 15.0637 14.9081 15.5032 14.7261C15.9426 14.5441 16.3417 14.2768 16.6772 13.9397L19.935 10.6819C20.6072 10.0013 20.9827 9.08236 20.9797 8.12579C20.9767 7.16922 20.5954 6.25269 19.919 5.57629C19.2426 4.89989 18.3261 4.51857 17.3695 4.51558C16.413 4.51258 15.4941 4.88816 14.8134 5.56031L13.7822 6.59156C13.6404 6.72628 13.4516 6.80027 13.256 6.79777C13.0604 6.79526 12.8736 6.71646 12.7353 6.57816C12.597 6.43986 12.5182 6.253 12.5157 6.05743C12.5132 5.86186 12.5872 5.67305 12.7219 5.53125L13.7531 4.5C14.2289 4.02406 14.7938 3.64651 15.4155 3.38893C16.0372 3.13134 16.7036 2.99876 17.3766 2.99876C18.0495 2.99876 18.7159 3.13134 19.3376 3.38893C19.9593 3.64651 20.5242 4.02406 21 4.5C21.4928 4.99402 21.8799 5.58316 22.1378 6.23155C22.3957 6.87994 22.5189 7.57403 22.5 8.27156ZM10.2187 17.4056L9.1875 18.4369C8.85115 18.7755 8.45084 19.0438 8.00986 19.2263C7.56887 19.4088 7.09601 19.5019 6.61875 19.5C5.90278 19.4994 5.20305 19.2866 4.60797 18.8885C4.01288 18.4904 3.54915 17.9249 3.27536 17.2633C3.00157 16.6018 2.93001 15.8739 3.06972 15.1717C3.20943 14.4695 3.55415 13.8245 4.06031 13.3181L7.3125 10.0603C7.82474 9.54537 8.47998 9.19635 9.19312 9.05858C9.90626 8.92081 10.6443 9.00066 11.3115 9.28775C11.9787 9.57484 12.5442 10.0559 12.9344 10.6685C13.3247 11.281 13.5217 11.9968 13.5 12.7228C13.4944 12.9217 13.5681 13.1147 13.7048 13.2593C13.8415 13.4039 14.03 13.4883 14.2289 13.4939C14.4278 13.4995 14.6208 13.4258 14.7654 13.2892C14.91 13.1525 14.9944 12.9639 15 12.765C15.018 12.0686 14.8943 11.3757 14.6365 10.7285C14.3786 10.0813 13.992 9.49326 13.5 9C12.5392 8.03965 11.2364 7.50017 9.87797 7.50017C8.51953 7.50017 7.2167 8.03965 6.25594 9L3 12.2578C2.28397 12.9736 1.79617 13.8855 1.59826 14.8784C1.40035 15.8713 1.5012 16.9005 1.88808 17.8361C2.27495 18.7717 2.93049 19.5716 3.77183 20.1348C4.61318 20.6979 5.60258 20.999 6.615 21C7.288 21.0019 7.95471 20.8704 8.57651 20.6129C9.19832 20.3554 9.76288 19.9771 10.2375 19.5L11.2687 18.4688C11.3901 18.3258 11.4535 18.1425 11.4465 17.9551C11.4394 17.7677 11.3624 17.5898 11.2306 17.4563C11.0988 17.3229 10.9218 17.2437 10.7345 17.2343C10.5472 17.2249 10.3632 17.286 10.2187 17.4056Z" fill="white" fill-opacity="0.9"/>
                    </svg>
                </div>
                <div class="icon-button icon-button-success">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M20.25 7.5H17.25V4.5C17.25 4.10218 17.092 3.72064 16.8107 3.43934C16.5294 3.15804 16.1478 3 15.75 3H3.75C3.35218 3 2.97064 3.15804 2.68934 3.43934C2.40804 3.72064 2.25 4.10218 2.25 4.5V16.5C2.25044 16.6411 2.29068 16.7792 2.36608 16.8985C2.44149 17.0177 2.54901 17.1133 2.67629 17.1742C2.80358 17.2351 2.94546 17.2589 3.08564 17.2428C3.22581 17.2266 3.3586 17.1713 3.46875 17.0831L6.75 14.4375V17.25C6.75 17.6478 6.90804 18.0294 7.18934 18.3107C7.47064 18.592 7.85218 18.75 8.25 18.75H17.0241L20.5312 21.5831C20.664 21.6905 20.8293 21.7493 21 21.75C21.1989 21.75 21.3897 21.671 21.5303 21.5303C21.671 21.3897 21.75 21.1989 21.75 21V9C21.75 8.60218 21.592 8.22064 21.3107 7.93934C21.0294 7.65804 20.6478 7.5 20.25 7.5ZM6.23906 12.9169L3.75 14.9297V4.5H15.75V12.75H6.71063C6.53897 12.75 6.37252 12.8089 6.23906 12.9169ZM20.25 19.4297L17.7609 17.4169C17.6282 17.3095 17.4629 17.2507 17.2922 17.25H8.25V14.25H15.75C16.1478 14.25 16.5294 14.092 16.8107 13.8107C17.092 13.5294 17.25 13.1478 17.25 12.75V9H20.25V19.4297Z" fill="white" fill-opacity="0.9"/>
                    </svg>
                </div>
            </div>

            <!-- 状态指示器 -->
            <div class="flex justify-center gap-md mt-lg">
                <div class="status status-up">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M4.00002 8.00002L8 4M8 4L12 8.00002M8 4V13" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                    </svg>
                    <span>0.00%</span>
                </div>
                <div class="status status-down">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M4.00002 8.99998L8 13M8 13L12 8.99998M8 13V4" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
                    </svg>
                    <span>0.00%</span>
                </div>
            </div>

            <!-- 导航项示例 -->
            <div class="mt-xl">
                <div class="nav-item active">
                    <div class="nav-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="7" height="7" rx="1" fill="currentColor"/>
                            <rect x="14" y="3" width="7" height="7" rx="1" fill="currentColor"/>
                            <rect x="14" y="14" width="7" height="7" rx="1" fill="currentColor"/>
                        </svg>
                    </div>
                    <span class="nav-text">仪表盘</span>
                </div>
            </div>
        </div>
    </div>

    <style>
        .input-group {
            position: relative;
        }
        
        .input-group .btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</body>
</html>
