<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景组件库</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .bg-preview {
            width: 100%;
            height: 300px;
            margin-bottom: 30px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .bg-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .bg-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 15px;
        }
        
        /* 背景样式 */
        .bg-home {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #eee;
        }
        
        .bg-ellipse {
            position: absolute;
            pointer-events: none;
        }
        
        .bg-ellipse svg {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        /* 缩放版本的椭圆位置 */
        .bg-ellipse-1 {
            left: 20%;
            top: 30%;
            width: 200px;
            height: 200px;
            transform: scale(0.8);
        }
        
        .bg-ellipse-2 {
            left: 10%;
            top: 40%;
            width: 200px;
            height: 180px;
            transform: scale(0.8);
        }
        
        .bg-ellipse-3 {
            left: -5%;
            top: 45%;
            width: 140px;
            height: 140px;
            transform: scale(0.8);
        }
        
        .bg-ellipse-4 {
            left: -15%;
            top: -10%;
            width: 80px;
            height: 45px;
            transform: scale(1.5);
        }
        
        .bg-ellipse-5 {
            left: 25%;
            top: -20%;
            width: 115px;
            height: 105px;
            transform: scale(0.8);
        }
    </style>
</head>
<body>
    <h1>背景组件库 - Figma 设计资源</h1>
    <p>从 Figma 节点 1:1574 提取的背景组件，包含 5 层椭圆渐变效果</p>
    
    <!-- 完整背景预览 -->
    <div class="bg-title">完整背景组件 (BG-Home)</div>
    <div class="bg-description">
        包含 5 个椭圆形渐变层的复杂背景效果，使用 SVG 滤镜实现模糊和透明度效果
    </div>
    <div class="bg-preview">
        <div class="bg-home">
            <!-- 背景渐变椭圆 1 -->
            <div class="bg-ellipse bg-ellipse-1">
                <svg viewBox="0 0 2491 2491" fill="none">
                    <g filter="url(#filter0_f_1_7179)" opacity="0.5">
                        <circle cx="1245.5" cy="1245.5" fill="#3234CA" r="645.5"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_7179" x="0" y="0" width="2491" height="2491" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_7179" stdDeviation="300"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 2 -->
            <div class="bg-ellipse bg-ellipse-2">
                <svg viewBox="0 0 2491 2433" fill="none">
                    <g filter="url(#filter0_f_1_7161)" opacity="0.25">
                        <path d="M1891 1186.54C1891 1543.04 1602 1832.04 1245.5 1832.04C889 1832.04 600 1543.04 600 1186.54C600 830.039 834.5 537.538 1263.5 611.539C1692.5 685.539 1891 830.039 1891 1186.54Z" fill="#3D83F5"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_7161" x="0" y="0" width="2491" height="2432.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_7161" stdDeviation="300"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 3 -->
            <div class="bg-ellipse bg-ellipse-3">
                <svg viewBox="0 0 1506 1501" fill="none">
                    <g filter="url(#filter0_f_1_6904)" opacity="0.55">
                        <path d="M1206 747.021C1206 997.206 1003.18 1200.02 753 1200.02C502.815 1200.02 300 997.206 300 747.021C300 496.836 334 231.521 700 316.021C1066 400.521 1206 496.836 1206 747.021Z" fill="#9B38C6"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_6904" x="0" y="0" width="1506" height="1500.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_6904" stdDeviation="150"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 4 -->
            <div class="bg-ellipse bg-ellipse-4">
                <svg viewBox="0 0 1123 889" fill="none">
                    <g filter="url(#filter0_f_1_6983)" opacity="0.8">
                        <path d="M823 556.532C823 647.482 604.406 518.479 456.178 518.479C307.95 518.479 300 555.629 300 464.679C300 373.729 420.162 300 568.39 300C716.618 300 823 465.582 823 556.532Z" fill="#668CFF"/>
                    </g>
                    <defs>
                        <filter id="filter0_f_1_6983" x="0" y="0" width="1123" height="889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feGaussianBlur result="effect1_foregroundBlur_1_6983" stdDeviation="150"/>
                        </filter>
                    </defs>
                </svg>
            </div>

            <!-- 背景渐变椭圆 5 -->
            <div class="bg-ellipse bg-ellipse-5">
                <svg viewBox="0 0 1100 1050" fill="none">
                    <g filter="url(#filter0_if_1_7000)">
                        <ellipse cx="550" cy="525" fill="#4848EF" fill-opacity="0.1" rx="370" ry="345"/>
                    </g>
                    <defs>
                        <filter id="filter0_if_1_7000" x="0" y="0" width="1100" height="1050" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                            <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                            <feOffset dy="-12"/>
                            <feGaussianBlur stdDeviation="45"/>
                            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.325191 0 0 0 0 0.266598 0 0 0 0 0.969709 0 0 0 1 0"/>
                            <feBlend in2="shape" mode="normal" result="effect1_innerShadow_1_7000"/>
                            <feGaussianBlur result="effect2_foregroundBlur_1_7000" stdDeviation="90"/>
                        </filter>
                    </defs>
                </svg>
            </div>
        </div>
    </div>

    <!-- 背景技术说明 -->
    <div class="bg-title">技术实现说明</div>
    <div class="bg-description">
        <h3>背景层次结构：</h3>
        <ul>
            <li><strong>椭圆 1</strong>: 颜色 #3234CA，透明度 50%，模糊半径 300px</li>
            <li><strong>椭圆 2</strong>: 颜色 #3D83F5，透明度 25%，模糊半径 300px</li>
            <li><strong>椭圆 3</strong>: 颜色 #9B38C6，透明度 55%，模糊半径 150px</li>
            <li><strong>椭圆 4</strong>: 颜色 #668CFF，透明度 80%，模糊半径 150px</li>
            <li><strong>椭圆 5</strong>: 颜色 #4848EF，透明度 10%，内阴影效果</li>
        </ul>
        
        <h3>SVG 滤镜效果：</h3>
        <ul>
            <li><strong>feGaussianBlur</strong>: 实现模糊效果</li>
            <li><strong>feColorMatrix</strong>: 颜色变换</li>
            <li><strong>feOffset</strong>: 阴影偏移</li>
            <li><strong>feComposite</strong>: 图层合成</li>
        </ul>
        
        <h3>使用方法：</h3>
        <p>复制相应的 HTML 和 CSS 代码到您的项目中，确保包含所有的 SVG 滤镜定义。</p>
    </div>

</body>
</html>
