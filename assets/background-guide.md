# 背景组件使用指南

## 概述

本指南详细说明了从 Figma 节点 1:1574 提取的背景组件的使用方法。该背景由 5 个椭圆形渐变层组成，使用 SVG 滤镜实现复杂的视觉效果。

## 文件结构

```
assets/
├── background.css          # 背景样式文件
├── background-filters.svg  # SVG 滤镜和形状定义
├── backgrounds.html        # 背景预览页面
└── background-guide.md     # 本使用指南
```

## 基础使用

### 1. 引入必要文件

```html
<!-- 引入背景样式 -->
<link rel="stylesheet" href="assets/background.css">

<!-- 引入 SVG 滤镜定义 -->
<div style="display: none;">
  <!-- 将 background-filters.svg 的内容复制到这里 -->
</div>
```

### 2. 基础 HTML 结构

```html
<div class="bg-home">
  <!-- 背景椭圆 1 -->
  <div class="bg-ellipse bg-ellipse-1">
    <svg viewBox="0 0 2491 2491" fill="none">
      <use href="#bg-ellipse-1"/>
    </svg>
  </div>

  <!-- 背景椭圆 2 -->
  <div class="bg-ellipse bg-ellipse-2">
    <svg viewBox="0 0 2491 2433" fill="none">
      <use href="#bg-ellipse-2"/>
    </svg>
  </div>

  <!-- 背景椭圆 3 -->
  <div class="bg-ellipse bg-ellipse-3">
    <svg viewBox="0 0 1506 1501" fill="none">
      <use href="#bg-ellipse-3"/>
    </svg>
  </div>

  <!-- 背景椭圆 4 -->
  <div class="bg-ellipse bg-ellipse-4">
    <svg viewBox="0 0 1123 889" fill="none">
      <use href="#bg-ellipse-4"/>
    </svg>
  </div>

  <!-- 背景椭圆 5 -->
  <div class="bg-ellipse bg-ellipse-5">
    <svg viewBox="0 0 1100 1050" fill="none">
      <use href="#bg-ellipse-5"/>
    </svg>
  </div>
</div>
```

## 背景变体

### 1. 标准版本
```html
<div class="bg-home">
  <!-- 完整的 5 层椭圆 -->
</div>
```

### 2. 简化版本（性能优化）
```html
<div class="bg-home bg-home-simple">
  <!-- 使用 CSS 渐变代替 SVG -->
</div>
```

### 3. 深色版本
```html
<div class="bg-home bg-home-dark">
  <!-- 适合深色主题的背景 -->
</div>
```

### 4. 高对比度版本
```html
<div class="bg-home bg-home-high-contrast">
  <!-- 高对比度版本，适合无障碍需求 -->
</div>
```

## 动画效果

### 添加浮动动画
```html
<div class="bg-ellipse bg-ellipse-1 bg-ellipse-animated">
  <!-- 椭圆会有轻微的浮动效果 -->
</div>
```

### 自定义动画
```css
.bg-ellipse-custom {
  animation: custom-float 8s ease-in-out infinite;
}

@keyframes custom-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
}
```

## 响应式适配

背景会自动适配不同屏幕尺寸：

- **桌面端**: 完整的椭圆尺寸和位置
- **平板端**: 适度缩放椭圆
- **手机端**: 大幅缩放并重新定位椭圆

### 自定义断点
```css
@media (max-width: 1200px) {
  .bg-ellipse-1 {
    transform: scale(0.9);
  }
}
```

## 性能优化

### 1. 使用简化版本
对于性能敏感的场景，使用 CSS 渐变代替 SVG：

```css
.bg-home-simple {
  background: linear-gradient(135deg, 
    rgba(50, 52, 202, 0.3) 0%,
    rgba(61, 131, 245, 0.2) 25%,
    rgba(155, 56, 198, 0.3) 50%,
    rgba(102, 140, 255, 0.4) 75%,
    rgba(72, 72, 239, 0.1) 100%
  );
}
```

### 2. 减少椭圆数量
只使用关键的椭圆层：

```html
<!-- 只使用椭圆 1 和 3 -->
<div class="bg-home">
  <div class="bg-ellipse bg-ellipse-1">...</div>
  <div class="bg-ellipse bg-ellipse-3">...</div>
</div>
```

### 3. 禁用动画
```css
@media (prefers-reduced-motion: reduce) {
  .bg-ellipse-animated {
    animation: none;
  }
}
```

## 颜色定制

### 修改椭圆颜色
```css
.bg-ellipse-1 circle {
  fill: #your-color !important;
}

.bg-ellipse-2 path {
  fill: #your-color !important;
}
```

### 使用 CSS 变量
```css
:root {
  --bg-color-1: #3234CA;
  --bg-color-2: #3D83F5;
  --bg-color-3: #9B38C6;
  --bg-color-4: #668CFF;
  --bg-color-5: #4848EF;
}

.bg-ellipse-1 circle {
  fill: var(--bg-color-1);
}
```

## 无障碍性

### 1. 减少动画
```css
@media (prefers-reduced-motion: reduce) {
  .bg-ellipse {
    animation: none !important;
  }
}
```

### 2. 高对比度支持
```css
@media (prefers-contrast: high) {
  .bg-home {
    background: #000 !important;
  }
  
  .bg-ellipse {
    opacity: 0.8 !important;
  }
}
```

### 3. 打印样式
```css
@media print {
  .bg-home {
    background: white !important;
  }
  
  .bg-ellipse {
    display: none !important;
  }
}
```

## 常见问题

### Q: 背景在某些浏览器中显示异常？
A: 确保浏览器支持 SVG 滤镜。对于旧浏览器，使用简化版本作为降级方案。

### Q: 背景影响页面性能？
A: 使用 `bg-home-simple` 类或减少椭圆数量。考虑使用 `will-change: transform` 优化动画性能。

### Q: 如何在深色主题中使用？
A: 使用 `bg-home-dark` 类，或自定义椭圆的透明度和颜色。

### Q: 背景在移动端显示不正确？
A: 检查 CSS 媒体查询是否正确应用。可能需要调整椭圆的缩放比例。

## 最佳实践

1. **性能优先**: 在移动端使用简化版本
2. **渐进增强**: 提供降级方案
3. **无障碍**: 支持减少动画和高对比度
4. **响应式**: 确保在所有设备上正常显示
5. **语义化**: 背景仅用于装饰，不包含重要信息

## 示例项目

查看 `backgrounds.html` 文件可以看到所有背景变体的实际效果。
