<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  
  <!-- 背景椭圆 1 的滤镜 -->
  <defs>
    <filter id="filter0_f_1_7179" x="0" y="0" width="2491" height="2491" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_1_7179" stdDeviation="300"/>
    </filter>
  </defs>

  <!-- 背景椭圆 2 的滤镜 -->
  <defs>
    <filter id="filter0_f_1_7161" x="0" y="0" width="2491" height="2432.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_1_7161" stdDeviation="300"/>
    </filter>
  </defs>

  <!-- 背景椭圆 3 的滤镜 -->
  <defs>
    <filter id="filter0_f_1_6904" x="0" y="0" width="1506" height="1500.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_1_6904" stdDeviation="150"/>
    </filter>
  </defs>

  <!-- 背景椭圆 4 的滤镜 -->
  <defs>
    <filter id="filter0_f_1_6983" x="0" y="0" width="1123" height="889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
      <feGaussianBlur result="effect1_foregroundBlur_1_6983" stdDeviation="150"/>
    </filter>
  </defs>

  <!-- 背景椭圆 5 的滤镜（内阴影效果） -->
  <defs>
    <filter id="filter0_if_1_7000" x="0" y="0" width="1100" height="1050" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
      <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="-12"/>
      <feGaussianBlur stdDeviation="45"/>
      <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.325191 0 0 0 0 0.266598 0 0 0 0 0.969709 0 0 0 1 0"/>
      <feBlend in2="shape" mode="normal" result="effect1_innerShadow_1_7000"/>
      <feGaussianBlur result="effect2_foregroundBlur_1_7000" stdDeviation="90"/>
    </filter>
  </defs>

  <!-- 背景椭圆形状定义 -->
  
  <!-- 椭圆 1: 主要蓝色圆形 -->
  <symbol id="bg-ellipse-1" viewBox="0 0 2491 2491">
    <g filter="url(#filter0_f_1_7179)" opacity="0.5">
      <circle cx="1245.5" cy="1245.5" fill="#3234CA" r="645.5"/>
    </g>
  </symbol>

  <!-- 椭圆 2: 次要蓝色不规则形状 -->
  <symbol id="bg-ellipse-2" viewBox="0 0 2491 2433">
    <g filter="url(#filter0_f_1_7161)" opacity="0.25">
      <path d="M1891 1186.54C1891 1543.04 1602 1832.04 1245.5 1832.04C889 1832.04 600 1543.04 600 1186.54C600 830.039 834.5 537.538 1263.5 611.539C1692.5 685.539 1891 830.039 1891 1186.54Z" fill="#3D83F5"/>
    </g>
  </symbol>

  <!-- 椭圆 3: 紫色不规则形状 -->
  <symbol id="bg-ellipse-3" viewBox="0 0 1506 1501">
    <g filter="url(#filter0_f_1_6904)" opacity="0.55">
      <path d="M1206 747.021C1206 997.206 1003.18 1200.02 753 1200.02C502.815 1200.02 300 997.206 300 747.021C300 496.836 334 231.521 700 316.021C1066 400.521 1206 496.836 1206 747.021Z" fill="#9B38C6"/>
    </g>
  </symbol>

  <!-- 椭圆 4: 浅蓝色不规则形状 -->
  <symbol id="bg-ellipse-4" viewBox="0 0 1123 889">
    <g filter="url(#filter0_f_1_6983)" opacity="0.8">
      <path d="M823 556.532C823 647.482 604.406 518.479 456.178 518.479C307.95 518.479 300 555.629 300 464.679C300 373.729 420.162 300 568.39 300C716.618 300 823 465.582 823 556.532Z" fill="#668CFF"/>
    </g>
  </symbol>

  <!-- 椭圆 5: 内阴影椭圆 -->
  <symbol id="bg-ellipse-5" viewBox="0 0 1100 1050">
    <g filter="url(#filter0_if_1_7000)">
      <ellipse cx="550" cy="525" fill="#4848EF" fill-opacity="0.1" rx="370" ry="345"/>
    </g>
  </symbol>

  <!-- 简化版本的背景形状（用于性能优化） -->
  
  <!-- 简化椭圆 1 -->
  <symbol id="bg-ellipse-1-simple" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="#3234CA" opacity="0.5"/>
  </symbol>

  <!-- 简化椭圆 2 -->
  <symbol id="bg-ellipse-2-simple" viewBox="0 0 100 100">
    <ellipse cx="50" cy="50" rx="45" ry="35" fill="#3D83F5" opacity="0.25"/>
  </symbol>

  <!-- 简化椭圆 3 -->
  <symbol id="bg-ellipse-3-simple" viewBox="0 0 100 100">
    <ellipse cx="50" cy="50" rx="35" ry="40" fill="#9B38C6" opacity="0.55"/>
  </symbol>

  <!-- 简化椭圆 4 -->
  <symbol id="bg-ellipse-4-simple" viewBox="0 0 100 100">
    <ellipse cx="50" cy="50" rx="30" ry="20" fill="#668CFF" opacity="0.8"/>
  </symbol>

  <!-- 简化椭圆 5 -->
  <symbol id="bg-ellipse-5-simple" viewBox="0 0 100 100">
    <ellipse cx="50" cy="50" rx="40" ry="35" fill="#4848EF" opacity="0.1"/>
  </symbol>

  <!-- 渐变定义 -->
  <defs>
    <!-- 主要渐变 -->
    <radialGradient id="bg-gradient-1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3234CA;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#3234CA;stop-opacity:0"/>
    </radialGradient>

    <radialGradient id="bg-gradient-2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3D83F5;stop-opacity:0.25"/>
      <stop offset="100%" style="stop-color:#3D83F5;stop-opacity:0"/>
    </radialGradient>

    <radialGradient id="bg-gradient-3" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#9B38C6;stop-opacity:0.55"/>
      <stop offset="100%" style="stop-color:#9B38C6;stop-opacity:0"/>
    </radialGradient>

    <radialGradient id="bg-gradient-4" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#668CFF;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#668CFF;stop-opacity:0"/>
    </radialGradient>

    <radialGradient id="bg-gradient-5" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4848EF;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#4848EF;stop-opacity:0"/>
    </radialGradient>

    <!-- 线性渐变版本 -->
    <linearGradient id="bg-linear-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3234CA;stop-opacity:0.3"/>
      <stop offset="25%" style="stop-color:#3D83F5;stop-opacity:0.2"/>
      <stop offset="50%" style="stop-color:#9B38C6;stop-opacity:0.3"/>
      <stop offset="75%" style="stop-color:#668CFF;stop-opacity:0.4"/>
      <stop offset="100%" style="stop-color:#4848EF;stop-opacity:0.1"/>
    </linearGradient>
  </defs>

</svg>
