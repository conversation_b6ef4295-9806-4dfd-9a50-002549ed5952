/* 
 * 背景组件样式
 * 从 Figma 节点 1:1574 提取
 * 包含 5 层椭圆渐变效果
 */

/* 背景容器 */
.bg-home {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #eee;
  overflow: hidden;
}

/* 背景椭圆通用样式 */
.bg-ellipse {
  position: absolute;
  pointer-events: none;
}

.bg-ellipse svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* 背景椭圆 1 - 主要蓝色渐变 */
.bg-ellipse-1 {
  left: 332px;
  top: 493px;
  width: 1291px;
  height: 1291px;
  transform: scale(1.465);
}

/* 背景椭圆 2 - 次要蓝色渐变 */
.bg-ellipse-2 {
  left: 116px;
  top: 684px;
  width: 1291px;
  height: 1232px;
  transform: scale(1.487);
}

/* 背景椭圆 3 - 紫色渐变 */
.bg-ellipse-3 {
  left: -40px;
  top: 750px;
  width: 906px;
  height: 900px;
  transform: scale(1.333);
}

/* 背景椭圆 4 - 浅蓝色渐变 */
.bg-ellipse-4 {
  left: -309px;
  top: -157px;
  width: 523px;
  height: 289px;
  transform: scale(2.038);
}

/* 背景椭圆 5 - 内阴影效果 */
.bg-ellipse-5 {
  left: 301px;
  top: -378px;
  width: 740px;
  height: 690px;
  transform: scale(1.261);
}

/* 响应式适配 */
@media (max-width: 768px) {
  /* 移动端背景椭圆调整 */
  .bg-ellipse-1 {
    left: 50%;
    transform: translateX(-50%) scale(0.8);
  }
  
  .bg-ellipse-2 {
    left: 50%;
    transform: translateX(-50%) scale(0.8);
  }
  
  .bg-ellipse-3 {
    left: -20%;
    transform: scale(0.6);
  }
  
  .bg-ellipse-4 {
    left: -50%;
    transform: scale(1.2);
  }
  
  .bg-ellipse-5 {
    left: 50%;
    transform: translateX(-50%) scale(0.7);
  }
}

@media (max-width: 480px) {
  /* 小屏幕进一步优化 */
  .bg-ellipse-1,
  .bg-ellipse-2 {
    transform: translateX(-50%) scale(0.6);
  }
  
  .bg-ellipse-3 {
    transform: scale(0.4);
  }
  
  .bg-ellipse-4 {
    transform: scale(0.8);
  }
  
  .bg-ellipse-5 {
    transform: translateX(-50%) scale(0.5);
  }
}

/* 背景变体 - 简化版本 */
.bg-home-simple {
  background: linear-gradient(135deg, 
    rgba(50, 52, 202, 0.3) 0%,
    rgba(61, 131, 245, 0.2) 25%,
    rgba(155, 56, 198, 0.3) 50%,
    rgba(102, 140, 255, 0.4) 75%,
    rgba(72, 72, 239, 0.1) 100%
  );
}

/* 背景变体 - 深色版本 */
.bg-home-dark {
  background: #1a1a1a;
}

.bg-home-dark .bg-ellipse-1 circle {
  fill: rgba(50, 52, 202, 0.3);
}

.bg-home-dark .bg-ellipse-2 path {
  fill: rgba(61, 131, 245, 0.15);
}

.bg-home-dark .bg-ellipse-3 path {
  fill: rgba(155, 56, 198, 0.25);
}

.bg-home-dark .bg-ellipse-4 path {
  fill: rgba(102, 140, 255, 0.4);
}

.bg-home-dark .bg-ellipse-5 ellipse {
  fill: rgba(72, 72, 239, 0.05);
}

/* 背景变体 - 高对比度版本 */
.bg-home-high-contrast {
  background: #000;
}

.bg-home-high-contrast .bg-ellipse-1 circle {
  fill: rgba(50, 52, 202, 0.8);
}

.bg-home-high-contrast .bg-ellipse-2 path {
  fill: rgba(61, 131, 245, 0.6);
}

.bg-home-high-contrast .bg-ellipse-3 path {
  fill: rgba(155, 56, 198, 0.7);
}

.bg-home-high-contrast .bg-ellipse-4 path {
  fill: rgba(102, 140, 255, 0.9);
}

.bg-home-high-contrast .bg-ellipse-5 ellipse {
  fill: rgba(72, 72, 239, 0.3);
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(var(--scale, 1));
  }
  50% {
    transform: translateY(-10px) scale(var(--scale, 1));
  }
}

.bg-ellipse-animated {
  animation: float 6s ease-in-out infinite;
}

.bg-ellipse-1.bg-ellipse-animated {
  --scale: 1.465;
  animation-delay: 0s;
}

.bg-ellipse-2.bg-ellipse-animated {
  --scale: 1.487;
  animation-delay: 1s;
}

.bg-ellipse-3.bg-ellipse-animated {
  --scale: 1.333;
  animation-delay: 2s;
}

.bg-ellipse-4.bg-ellipse-animated {
  --scale: 2.038;
  animation-delay: 3s;
}

.bg-ellipse-5.bg-ellipse-animated {
  --scale: 1.261;
  animation-delay: 4s;
}

/* 性能优化 */
.bg-ellipse {
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* 可访问性 */
@media (prefers-reduced-motion: reduce) {
  .bg-ellipse-animated {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .bg-home {
    background: white !important;
  }
  
  .bg-ellipse {
    display: none !important;
  }
}
