/* 
 * 毛玻璃效果组件库
 * 从 Figma 节点 1:429 提取的毛玻璃效果
 * 支持多种强度和样式变体
 */

/* 基础毛玻璃效果变量 */
:root {
  /* 毛玻璃背景颜色 */
  --glass-bg-light: rgba(255, 255, 255, 0.1);
  --glass-bg-medium: rgba(255, 255, 255, 0.15);
  --glass-bg-dark: rgba(45, 45, 63, 0.8);
  --glass-bg-darker: rgba(0, 0, 0, 0.3);
  
  /* 毛玻璃边框 */
  --glass-border-light: rgba(255, 255, 255, 0.1);
  --glass-border-medium: rgba(255, 255, 255, 0.2);
  --glass-border-strong: rgba(255, 255, 255, 0.3);
  
  /* 模糊强度 */
  --blur-light: 10px;
  --blur-medium: 20px;
  --blur-strong: 30px;
  --blur-extreme: 40px;
  
  /* 阴影效果 */
  --glass-shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-shadow-medium: 0 12px 32px rgba(6, 1, 43, 0.1);
  --glass-shadow-strong: 0 16px 40px rgba(0, 0, 0, 0.2);
}

/* 基础毛玻璃类 */
.glass {
  backdrop-filter: blur(var(--blur-medium));
  -webkit-backdrop-filter: blur(var(--blur-medium));
  background: var(--glass-bg-medium);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-medium);
}

/* 毛玻璃强度变体 */
.glass-light {
  backdrop-filter: blur(var(--blur-light));
  -webkit-backdrop-filter: blur(var(--blur-light));
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.glass-medium {
  backdrop-filter: blur(var(--blur-medium));
  -webkit-backdrop-filter: blur(var(--blur-medium));
  background: var(--glass-bg-medium);
  border: 1px solid var(--glass-border-medium);
  box-shadow: var(--glass-shadow-medium);
}

.glass-strong {
  backdrop-filter: blur(var(--blur-strong));
  -webkit-backdrop-filter: blur(var(--blur-strong));
  background: var(--glass-bg-dark);
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--glass-shadow-strong);
}

.glass-extreme {
  backdrop-filter: blur(var(--blur-extreme));
  -webkit-backdrop-filter: blur(var(--blur-extreme));
  background: var(--glass-bg-darker);
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--glass-shadow-strong);
}

/* 毛玻璃卡片组件 */
.glass-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(45, 45, 63, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(45, 45, 63, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0px 16px 40px 0px rgba(6, 1, 43, 0.15);
}

/* 毛玻璃按钮组件 */
.glass-button {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.glass-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.15);
}

/* 毛玻璃输入框组件 */
.glass-input {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.glass-input:focus {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(114, 112, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(114, 112, 255, 0.2);
}

/* 毛玻璃导航栏组件 */
.glass-nav {
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  background: rgba(45, 45, 63, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 毛玻璃模态框组件 */
.glass-modal {
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  background: rgba(45, 45, 63, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  box-shadow: 0px 20px 60px 0px rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 毛玻璃侧边栏组件 */
.glass-sidebar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(45, 45, 63, 0.8);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  z-index: 50;
}

/* 毛玻璃通知组件 */
.glass-notification {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(45, 45, 63, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 毛玻璃工具提示组件 */
.glass-tooltip {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: white;
  position: absolute;
  z-index: 1000;
  pointer-events: none;
}

/* 毛玻璃下拉菜单组件 */
.glass-dropdown {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(45, 45, 63, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  box-shadow: 0px 12px 32px 0px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: absolute;
  z-index: 100;
}

.glass-dropdown-item {
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: background 0.2s ease;
}

.glass-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 浏览器兼容性降级 */
@supports not (backdrop-filter: blur(10px)) {
  .glass,
  .glass-light,
  .glass-medium,
  .glass-strong,
  .glass-extreme,
  .glass-card,
  .glass-button,
  .glass-input,
  .glass-nav,
  .glass-modal,
  .glass-sidebar,
  .glass-notification,
  .glass-tooltip,
  .glass-dropdown {
    background: rgba(45, 45, 63, 0.95);
  }
}

/* 性能优化 */
.glass,
.glass-card,
.glass-modal {
  will-change: backdrop-filter;
  transform: translateZ(0);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .glass-modal {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    margin: 20px;
    width: calc(100% - 40px);
    height: auto;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
  }
  
  .glass-sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg-light: rgba(255, 255, 255, 0.05);
    --glass-bg-medium: rgba(255, 255, 255, 0.1);
    --glass-bg-dark: rgba(0, 0, 0, 0.6);
    --glass-bg-darker: rgba(0, 0, 0, 0.8);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .glass,
  .glass-card,
  .glass-button,
  .glass-input,
  .glass-modal {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.5);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .glass-button,
  .glass-input,
  .glass-notification {
    transition: none;
  }
  
  .glass-notification {
    animation: none;
  }
}
