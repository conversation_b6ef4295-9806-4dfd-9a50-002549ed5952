<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毛玻璃效果组件库</title>
    <link rel="stylesheet" href="glass-effects.css">
    <link rel="stylesheet" href="background.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Noto Sans SC', sans-serif;
            color: white;
            overflow-x: hidden;
        }
        
        .demo-container {
            position: relative;
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: white;
        }
        
        .demo-subtitle {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .demo-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .demo-item {
            margin-bottom: 20px;
        }
        
        /* 示例内容样式 */
        .sample-content {
            padding: 20px;
            text-align: center;
        }
        
        .sample-content h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .sample-content p {
            margin: 0;
            font-size: 14px;
            opacity: 0.8;
        }
        
        /* 按钮示例 */
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        /* 输入框示例 */
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        /* 导航栏示例 */
        .nav-demo {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s ease;
        }
        
        .nav-link:hover {
            color: white;
        }
        
        /* 通知示例 */
        .notification-demo {
            position: relative;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 工具提示示例 */
        .tooltip-demo {
            position: relative;
            display: inline-block;
            padding: 10px 20px;
            background: rgba(114, 112, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
        }
        
        .tooltip-demo:hover .glass-tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .glass-tooltip {
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 5px;
        }
        
        /* 下拉菜单示例 */
        .dropdown-demo {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-demo:hover .glass-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .glass-dropdown {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            top: 100%;
            left: 0;
            margin-top: 5px;
            min-width: 150px;
        }
    </style>
</head>
<body>
    <!-- 背景 -->
    <div class="bg-home">
        <!-- 背景椭圆 1 -->
        <div class="bg-ellipse bg-ellipse-1">
            <svg viewBox="0 0 2491 2491" fill="none">
                <g filter="url(#filter0_f_1_7179)" opacity="0.5">
                    <circle cx="1245.5" cy="1245.5" fill="#3234CA" r="645.5"/>
                </g>
                <defs>
                    <filter id="filter0_f_1_7179" x="0" y="0" width="2491" height="2491" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                        <feGaussianBlur result="effect1_foregroundBlur_1_7179" stdDeviation="300"/>
                    </filter>
                </defs>
            </svg>
        </div>

        <!-- 背景椭圆 2 -->
        <div class="bg-ellipse bg-ellipse-2">
            <svg viewBox="0 0 2491 2433" fill="none">
                <g filter="url(#filter0_f_1_7161)" opacity="0.25">
                    <path d="M1891 1186.54C1891 1543.04 1602 1832.04 1245.5 1832.04C889 1832.04 600 1543.04 600 1186.54C600 830.039 834.5 537.538 1263.5 611.539C1692.5 685.539 1891 830.039 1891 1186.54Z" fill="#3D83F5"/>
                </g>
                <defs>
                    <filter id="filter0_f_1_7161" x="0" y="0" width="2491" height="2432.04" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                        <feGaussianBlur result="effect1_foregroundBlur_1_7161" stdDeviation="300"/>
                    </filter>
                </defs>
            </svg>
        </div>

        <!-- 背景椭圆 3 -->
        <div class="bg-ellipse bg-ellipse-3">
            <svg viewBox="0 0 1506 1501" fill="none">
                <g filter="url(#filter0_f_1_6904)" opacity="0.55">
                    <path d="M1206 747.021C1206 997.206 1003.18 1200.02 753 1200.02C502.815 1200.02 300 997.206 300 747.021C300 496.836 334 231.521 700 316.021C1066 400.521 1206 496.836 1206 747.021Z" fill="#9B38C6"/>
                </g>
                <defs>
                    <filter id="filter0_f_1_6904" x="0" y="0" width="1506" height="1500.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape"/>
                        <feGaussianBlur result="effect1_foregroundBlur_1_6904" stdDeviation="150"/>
                    </filter>
                </defs>
            </svg>
        </div>
    </div>

    <div class="demo-container">
        <h1 class="demo-title">毛玻璃效果组件库</h1>
        <p style="text-align: center; color: rgba(255, 255, 255, 0.7); margin-bottom: 40px;">
            从 Figma 设计中提取的完整毛玻璃效果系统
        </p>

        <div class="demo-grid">
            <!-- 基础毛玻璃效果 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">基础毛玻璃效果</h2>
                <div class="demo-description">
                    不同强度的毛玻璃效果，从轻微到极强的模糊程度
                </div>
                
                <div class="demo-item">
                    <div class="glass-light sample-content">
                        <h3>轻微毛玻璃</h3>
                        <p>blur(10px) + 轻微背景</p>
                    </div>
                </div>
                
                <div class="demo-item">
                    <div class="glass-medium sample-content">
                        <h3>中等毛玻璃</h3>
                        <p>blur(20px) + 中等背景</p>
                    </div>
                </div>
                
                <div class="demo-item">
                    <div class="glass-strong sample-content">
                        <h3>强烈毛玻璃</h3>
                        <p>blur(30px) + 深色背景</p>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃卡片 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃卡片</h2>
                <div class="demo-description">
                    主要用于内容展示的卡片组件，具有悬停效果
                </div>
                
                <div class="demo-item">
                    <div class="glass-card sample-content">
                        <h3>毛玻璃卡片</h3>
                        <p>悬停查看效果</p>
                        <p style="margin-top: 10px; font-size: 12px;">
                            backdrop-filter: blur(20px)<br>
                            background: rgba(45, 45, 63, 0.8)
                        </p>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃按钮 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃按钮</h2>
                <div class="demo-description">
                    具有毛玻璃效果的交互按钮
                </div>
                
                <div class="demo-item">
                    <div class="button-group">
                        <button class="glass-button">主要按钮</button>
                        <button class="glass-button">次要按钮</button>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃输入框 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃输入框</h2>
                <div class="demo-description">
                    具有毛玻璃效果的表单输入组件
                </div>
                
                <div class="demo-item">
                    <div class="input-group">
                        <input type="text" class="glass-input" placeholder="请输入用户名">
                        <input type="password" class="glass-input" placeholder="请输入密码">
                        <input type="email" class="glass-input" placeholder="请输入邮箱">
                    </div>
                </div>
            </div>

            <!-- 毛玻璃导航栏 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃导航栏</h2>
                <div class="demo-description">
                    适用于页面顶部的导航组件
                </div>
                
                <div class="demo-item">
                    <div class="glass-nav nav-demo">
                        <div style="font-weight: 600;">Logo</div>
                        <div class="nav-links">
                            <a href="#" class="nav-link">首页</a>
                            <a href="#" class="nav-link">产品</a>
                            <a href="#" class="nav-link">关于</a>
                            <a href="#" class="nav-link">联系</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃通知 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃通知</h2>
                <div class="demo-description">
                    用于显示系统通知的组件
                </div>
                
                <div class="demo-item">
                    <div class="notification-demo">
                        <div class="glass-notification">
                            <h3 style="margin: 0 0 8px 0; font-size: 14px;">系统通知</h3>
                            <p style="margin: 0; font-size: 12px; opacity: 0.8;">
                                这是一个毛玻璃效果的通知组件
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃工具提示 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃工具提示</h2>
                <div class="demo-description">
                    悬停显示的工具提示组件
                </div>
                
                <div class="demo-item" style="text-align: center;">
                    <div class="tooltip-demo">
                        悬停查看提示
                        <div class="glass-tooltip">
                            这是毛玻璃工具提示
                        </div>
                    </div>
                </div>
            </div>

            <!-- 毛玻璃下拉菜单 -->
            <div class="demo-section">
                <h2 class="demo-subtitle">毛玻璃下拉菜单</h2>
                <div class="demo-description">
                    悬停显示的下拉菜单组件
                </div>
                
                <div class="demo-item" style="text-align: center;">
                    <div class="dropdown-demo">
                        <button class="glass-button">下拉菜单</button>
                        <div class="glass-dropdown">
                            <div class="glass-dropdown-item">选项 1</div>
                            <div class="glass-dropdown-item">选项 2</div>
                            <div class="glass-dropdown-item">选项 3</div>
                            <div class="glass-dropdown-item">选项 4</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
