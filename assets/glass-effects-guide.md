# 毛玻璃效果使用指南

## 概述

本指南详细说明了从 Figma 节点 1:429 提取的毛玻璃效果的实现和使用方法。毛玻璃效果（Glassmorphism）是一种现代 UI 设计趋势，通过背景模糊和半透明效果创造出类似磨砂玻璃的视觉效果。

## 技术原理

### 核心 CSS 属性

```css
/* 毛玻璃效果的核心属性 */
.glass-effect {
  backdrop-filter: blur(20px);           /* 背景模糊 */
  -webkit-backdrop-filter: blur(20px);   /* Safari 兼容 */
  background: rgba(45, 45, 63, 0.8);     /* 半透明背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 细边框 */
  box-shadow: 0px 12px 32px rgba(6, 1, 43, 0.1); /* 阴影 */
}
```

### 关键技术要点

1. **backdrop-filter**: 对元素后面的内容应用滤镜效果
2. **半透明背景**: 使用 rgba 或 hsla 颜色值
3. **细边框**: 增强玻璃质感
4. **阴影**: 创造浮起效果
5. **浏览器前缀**: 确保 Safari 兼容性

## 毛玻璃强度等级

### 1. 轻微毛玻璃 (Light)
```css
.glass-light {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
```
**适用场景**: 工具提示、轻量级覆盖层

### 2. 中等毛玻璃 (Medium)
```css
.glass-medium {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
```
**适用场景**: 按钮、输入框、小型卡片

### 3. 强烈毛玻璃 (Strong)
```css
.glass-strong {
  backdrop-filter: blur(30px);
  background: rgba(45, 45, 63, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
```
**适用场景**: 主要卡片、导航栏、侧边栏

### 4. 极强毛玻璃 (Extreme)
```css
.glass-extreme {
  backdrop-filter: blur(40px);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
```
**适用场景**: 模态框、全屏覆盖层

## 组件实现

### 毛玻璃卡片

```html
<div class="glass-card">
  <div class="card-content">
    <h3>卡片标题</h3>
    <p>卡片内容</p>
  </div>
</div>
```

```css
.glass-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(45, 45, 63, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0px 12px 32px rgba(6, 1, 43, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(45, 45, 63, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}
```

### 毛玻璃按钮

```html
<button class="glass-button">
  <span>按钮文字</span>
</button>
```

```css
.glass-button {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}
```

### 毛玻璃输入框

```html
<div class="input-group">
  <input type="text" class="glass-input" placeholder="请输入内容">
</div>
```

```css
.glass-input {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.glass-input:focus {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(114, 112, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(114, 112, 255, 0.2);
}
```

## 设计原则

### 1. 层次结构
- **前景元素**: 使用较强的毛玻璃效果
- **背景元素**: 使用较弱的毛玻璃效果
- **重要内容**: 确保在毛玻璃元素上清晰可见

### 2. 颜色搭配
- **深色背景**: 使用浅色半透明背景
- **浅色背景**: 使用深色半透明背景
- **对比度**: 确保文字与背景有足够对比度

### 3. 边框和阴影
- **细边框**: 1-2px 的半透明边框
- **柔和阴影**: 使用较大的模糊半径
- **多层阴影**: 结合内阴影和外阴影

## 性能优化

### 1. 减少重绘
```css
.glass-element {
  will-change: backdrop-filter;
  transform: translateZ(0);
}
```

### 2. 条件应用
```css
/* 只在支持的浏览器中应用 */
@supports (backdrop-filter: blur(10px)) {
  .glass-effect {
    backdrop-filter: blur(20px);
  }
}
```

### 3. 移动端优化
```css
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(15px); /* 减少模糊强度 */
  }
}
```

## 浏览器兼容性

### 支持情况
- **Chrome**: 76+ ✅
- **Firefox**: 103+ ✅
- **Safari**: 9+ ✅
- **Edge**: 79+ ✅

### 降级方案
```css
/* 不支持 backdrop-filter 的降级 */
@supports not (backdrop-filter: blur(10px)) {
  .glass-effect {
    background: rgba(45, 45, 63, 0.95);
  }
}
```

## 无障碍性考虑

### 1. 对比度
```css
/* 确保文字对比度 */
.glass-content {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
```

### 2. 高对比度模式
```css
@media (prefers-contrast: high) {
  .glass-effect {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.5);
  }
}
```

### 3. 减少动画
```css
@media (prefers-reduced-motion: reduce) {
  .glass-effect {
    transition: none;
  }
}
```

## 最佳实践

### 1. 适度使用
- 不要过度使用毛玻璃效果
- 保持页面整体的视觉平衡
- 确保重要内容的可读性

### 2. 背景要求
- 毛玻璃效果需要有内容丰富的背景
- 避免在纯色背景上使用
- 确保背景有足够的视觉层次

### 3. 交互反馈
- 为交互元素添加悬停效果
- 使用过渡动画增强用户体验
- 保持一致的交互模式

## 常见问题

### Q: 毛玻璃效果在某些浏览器中不显示？
A: 检查浏览器版本，使用 `@supports` 查询提供降级方案。

### Q: 毛玻璃效果影响性能？
A: 合理使用，避免在大面积或频繁变化的元素上使用。

### Q: 文字在毛玻璃上不清晰？
A: 增加文字阴影，调整背景透明度，确保足够的对比度。

### Q: 移动端效果不佳？
A: 减少模糊强度，简化效果，考虑使用纯色背景作为降级。

## 示例代码

查看 `glass-effects.html` 文件可以看到所有毛玻璃效果的实际应用示例。
