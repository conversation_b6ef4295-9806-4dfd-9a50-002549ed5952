/* 设计系统变量 */
:root {
  /* 颜色系统 */
  --white-text-1: #ffffffe5; /* 主要文本 */
  --white-text-2: #ffffff99; /* 次要文本 */
  --white-text-3: #ffffff4d; /* 辅助文本 */
  --dark-text-1: #000000d9; /* 深色文本 */
  --text-highlight: #7270ff; /* 高亮文本 */
  --dark-bg: #2d2d3f; /* 深色背景 */
  --dark-line-1: #ffffff1a; /* 浅边框 */
  --dark-line-2: #ffffff26; /* 深边框 */
  
  /* 背景渐变颜色 */
  --gradient-blue-1: #3234CA;
  --gradient-blue-2: #3D83F5;
  --gradient-purple: #9B38C6;
  --gradient-blue-3: #668CFF;
  --gradient-blue-4: #4848EF;
  
  /* 字体系统 */
  --font-family: 'Noto Sans SC', sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 阴影系统 */
  --shadow-card: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
  --shadow-inner: inset 0 -12px 90px rgba(52, 43, 247, 0.1);
  
  /* 圆角系统 */
  --radius-sm: 8px;
  --radius-md: 16px;
  --radius-lg: 20px;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: #eee;
  overflow-x: hidden;
  min-height: 100vh;
}

/* 应用容器 */
.app-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* 背景层 */
.bg-home {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #eee;
}

/* 背景椭圆通用样式 */
.bg-ellipse {
  position: absolute;
  pointer-events: none;
}

.bg-ellipse svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* 背景椭圆 1 */
.bg-ellipse-1 {
  left: 332px;
  top: 493px;
  width: 1291px;
  height: 1291px;
  transform: scale(1.465);
}

/* 背景椭圆 2 */
.bg-ellipse-2 {
  left: 116px;
  top: 684px;
  width: 1291px;
  height: 1232px;
  transform: scale(1.487);
}

/* 背景椭圆 3 */
.bg-ellipse-3 {
  left: -40px;
  top: 750px;
  width: 906px;
  height: 900px;
  transform: scale(1.333);
}

/* 背景椭圆 4 */
.bg-ellipse-4 {
  left: -309px;
  top: -157px;
  width: 523px;
  height: 289px;
  transform: scale(2.038);
}

/* 背景椭圆 5 */
.bg-ellipse-5 {
  left: 301px;
  top: -378px;
  width: 740px;
  height: 690px;
  transform: scale(1.261);
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
}

/* 卡片通用样式 */
.notification-card,
.funnel-card {
  background: var(--dark-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* 通知卡片 */
.notification-card {
  width: 344px;
  height: 336px;
  display: flex;
  flex-direction: column;
}

/* 合作漏斗卡片 */
.funnel-card {
  width: 344px;
  height: 336px;
  display: flex;
  flex-direction: column;
}

/* 卡片头部 */
.card-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid var(--dark-line-1);
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--spacing-sm);
}

.icon-container {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--white-text-1);
  flex: 1;
}

.header-dropdown {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: var(--white-text-1);
  cursor: pointer;
}

/* 通知列表 */
.notification-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.notification-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notification-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.notification-text {
  font-size: var(--font-size-sm);
  color: var(--white-text-1);
  line-height: 1.4;
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--white-text-2);
}

/* 漏斗统计 */
.funnel-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--dark-line-1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--white-text-1);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .notification-card,
  .funnel-card {
    width: 100%;
    max-width: 400px;
  }
  
  /* 调整背景椭圆在移动端的位置 */
  .bg-ellipse-1 {
    left: 50%;
    transform: translateX(-50%) scale(0.8);
  }
  
  .bg-ellipse-2 {
    left: 50%;
    transform: translateX(-50%) scale(0.8);
  }
  
  .bg-ellipse-3 {
    left: -20%;
    transform: scale(0.6);
  }
  
  .bg-ellipse-4 {
    left: -50%;
    transform: scale(1.2);
  }
  
  .bg-ellipse-5 {
    left: 50%;
    transform: translateX(-50%) scale(0.7);
  }
}

/* 动画效果 */
.notification-card,
.funnel-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.notification-card:hover,
.funnel-card:hover {
  transform: translateY(-4px);
  box-shadow: 0px 16px 40px 0px rgba(6, 1, 43, 0.15);
}

.header-dropdown:hover {
  background: rgba(255, 255, 255, 0.05);
}

.notification-item {
  transition: background 0.2s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
}

/* 加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-card,
.funnel-card {
  animation: fadeIn 0.6s ease-out;
}

.notification-card {
  animation-delay: 0.1s;
}

.funnel-card {
  animation-delay: 0.2s;
}

/* 组件展示区域 */
.components-showcase {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--dark-bg);
  border-left: 1px solid var(--dark-line-1);
  padding: var(--spacing-lg);
  overflow-y: auto;
  z-index: 1000;
  transition: right 0.3s ease;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
}

.components-showcase:hover {
  right: 0;
}

.components-showcase::before {
  content: '组件库';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  background: var(--text-highlight);
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

.showcase-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--white-text-1);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.component-section {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--dark-line-1);
}

.component-section:last-child {
  border-bottom: none;
}

.component-title {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--white-text-1);
  margin-bottom: var(--spacing-md);
}

/* Logo 组件样式 */
.logo-component {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: rgba(0, 0, 0, 0.3);
  border-radius: var(--radius-sm);
  border: 1px solid var(--dark-line-1);
}

.logo-icon {
  width: 24px;
  height: 19px;
  flex-shrink: 0;
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-text-top,
.logo-text-bottom {
  height: 8px;
}

/* 输入框组件样式 */
.input-component {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
}

.input-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.input-divider {
  width: 1px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.input-field {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--white-text-1);
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.send-code-btn {
  background: var(--text-highlight);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.send-code-btn:hover {
  background: #5a58e8;
}

/* 按钮组件样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  font-family: var(--font-family);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--text-highlight);
  color: white;
}

.btn-primary:hover {
  background: #5a58e8;
  transform: translateY(-1px);
}

/* 图标按钮组件样式 */
.icon-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.icon-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.icon-btn:hover {
  transform: scale(1.05);
}

.icon-btn-connect {
  background: #3bb1b7;
}

.icon-btn-reply {
  background: #3ab764;
}

/* 状态标签组件样式 */
.status-tags {
  display: flex;
  gap: var(--spacing-md);
}

.status-tag {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(10, 2, 41, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.status-up {
  color: rgba(0, 255, 229, 0.6);
}

.status-down {
  color: rgba(255, 119, 0, 0.6);
}

/* 导航列表项组件样式 */
.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background 0.2s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.nav-item-selected {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.nav-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.nav-text {
  font-size: var(--font-size-base);
  color: var(--white-text-1);
  font-weight: 400;
}
