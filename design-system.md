# 合作管理系统 - 设计规范指南

## 概述

本文档基于 Figma 设计原型创建，提供了完整的设计系统规范，用于指导 AI 生成新的界面功能时保持设计一致性。

## 颜色系统

### 主要颜色变量

```css
/* 文本颜色 */
--white-text-1: #ffffffe5;  /* 主要文本，透明度 90% */
--white-text-2: #ffffff99;  /* 次要文本，透明度 60% */
--white-text-3: #ffffff4d;  /* 辅助文本，透明度 30% */
--dark-text-1: #000000d9;  /* 深色文本，透明度 85% */

/* 背景颜色 */
--dark-bg: #2d2d3f;        /* 主要深色背景 */
--text-highlight: #7270ff; /* 高亮颜色 */

/* 边框颜色 */
--dark-line-1: #ffffff1a;  /* 浅边框，透明度 10% */
--dark-line-2: #ffffff26;  /* 深边框，透明度 15% */

/* 背景渐变颜色 */
--gradient-blue-1: #3234CA;
--gradient-blue-2: #3D83F5;
--gradient-purple: #9B38C6;
--gradient-blue-3: #668CFF;
--gradient-blue-4: #4848EF;
```

### 颜色使用规则

1. **主要文本**: 使用 `--white-text-1` 用于标题和重要信息
2. **次要文本**: 使用 `--white-text-2` 用于描述性文本
3. **辅助文本**: 使用 `--white-text-3` 用于时间戳和不重要信息
4. **背景**: 卡片背景统一使用 `--dark-bg`
5. **边框**: 使用 `--dark-line-1` 用于分隔线，`--dark-line-2` 用于强调边框

## 字体系统

### 字体族
- **主字体**: 'Noto Sans SC', sans-serif
- **备用字体**: system-ui, -apple-system, sans-serif

### 字体大小
```css
--font-size-xs: 12px;   /* 时间戳、标签 */
--font-size-sm: 14px;   /* 正文、描述 */
--font-size-base: 16px; /* 标题、按钮 */
--font-size-lg: 18px;   /* 大标题 */
```

### 字体权重
- **Regular (400)**: 正文内容
- **Medium (500)**: 卡片标题、按钮
- **SemiBold (600)**: 数据展示、重要数值

## 间距系统

```css
--spacing-xs: 4px;   /* 紧密间距 */
--spacing-sm: 8px;   /* 小间距 */
--spacing-md: 16px;  /* 标准间距 */
--spacing-lg: 24px;  /* 大间距 */
--spacing-xl: 32px;  /* 超大间距 */
```

### 间距使用规则
- **组件内部**: 使用 xs-sm 间距
- **组件之间**: 使用 md-lg 间距
- **页面边距**: 使用 lg-xl 间距

## 阴影系统

```css
--shadow-card: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
--shadow-inner: inset 0 -12px 90px rgba(52, 43, 247, 0.1);
```

### 阴影使用规则
- **卡片阴影**: 使用 `--shadow-card` 创建浮起效果
- **内阴影**: 用于特殊背景效果

## 圆角系统

```css
--radius-sm: 8px;   /* 小元素圆角 */
--radius-md: 16px;  /* 卡片圆角 */
--radius-lg: 20px;  /* 头像、特殊元素 */
```

## 组件规范

### 卡片组件 (Card)

**基础结构**:
```html
<div class="card">
  <div class="card-header">
    <div class="header-content">
      <div class="icon-container"><!-- 图标 --></div>
      <h2 class="card-title">标题</h2>
      <div class="header-dropdown"><!-- 可选下拉菜单 --></div>
    </div>
  </div>
  <div class="card-content">
    <!-- 卡片内容 -->
  </div>
</div>
```

**样式规范**:
- 背景: `var(--dark-bg)`
- 圆角: `var(--radius-md)`
- 阴影: `var(--shadow-card)`
- 头部高度: 56px
- 头部背景: `rgba(0, 0, 0, 0.1)`
- 头部边框: `1px solid var(--dark-line-1)`

### 通知项组件 (Notification Item)

**基础结构**:
```html
<div class="notification-item">
  <div class="notification-avatar">
    <div class="avatar-icon"><!-- 图标 --></div>
  </div>
  <div class="notification-content">
    <div class="notification-text">主要文本</div>
    <div class="notification-time">时间信息</div>
  </div>
</div>
```

**样式规范**:
- 头像尺寸: 40x40px
- 头像背景: `rgba(255, 255, 255, 0.1)`
- 头像圆角: `var(--radius-lg)`
- 文本颜色: 主要文本使用 `--white-text-1`，时间使用 `--white-text-2`

### 统计项组件 (Stat Item)

**基础结构**:
```html
<div class="stat-item">
  <span class="stat-label">标签</span>
  <span class="stat-value">数值</span>
</div>
```

**样式规范**:
- 内边距: `var(--spacing-md) var(--spacing-lg)`
- 边框: `1px solid var(--dark-line-1)` (底部)
- 标签颜色: `var(--white-text-1)`
- 数值颜色: `#ffffff`，字体权重 600

## 背景系统

### 渐变背景规范

背景由多个椭圆形渐变叠加组成，使用 SVG 滤镜实现模糊效果：

1. **椭圆 1**: 颜色 `#3234CA`，透明度 50%，模糊半径 300px
2. **椭圆 2**: 颜色 `#3D83F5`，透明度 25%，模糊半径 300px
3. **椭圆 3**: 颜色 `#9B38C6`，透明度 55%，模糊半径 150px
4. **椭圆 4**: 颜色 `#668CFF`，透明度 80%，模糊半径 150px
5. **椭圆 5**: 颜色 `#4848EF`，透明度 10%，内阴影效果

### 背景使用规则
- 基础背景色: `#eee`
- 渐变椭圆使用绝对定位
- 椭圆层级低于内容层 (z-index < 10)
- 移动端需要调整椭圆大小和位置

## 图标系统

### 图标规范
- **尺寸**: 16px, 20px, 24px, 32px
- **颜色**: 白色，透明度 90%
- **线条宽度**: 1.25px - 1.5px
- **风格**: 线性图标，圆角端点

### 常用图标
- **漏斗图标**: 用于筛选和数据展示
- **勾选图标**: 用于完成状态
- **下拉箭头**: 用于展开菜单

## 动画效果

### 悬停效果
```css
/* 卡片悬停 */
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0px 16px 40px 0px rgba(6, 1, 43, 0.15);
}

/* 按钮悬停 */
.button:hover {
  background: rgba(255, 255, 255, 0.05);
}
```

### 加载动画
```css
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 响应式设计

### 断点
- **移动端**: max-width: 768px
- **桌面端**: min-width: 769px

### 移动端适配
- 卡片宽度: 100%，最大宽度 400px
- 布局方向: 垂直排列
- 间距: 减少为 `var(--spacing-md)`
- 背景椭圆: 缩放和重新定位

## 使用指南

### 创建新组件时
1. 遵循现有的颜色变量
2. 使用统一的间距系统
3. 保持字体规范一致
4. 添加适当的悬停和动画效果
5. 确保移动端适配

### 扩展设计系统时
1. 新增颜色变量应遵循命名规范
2. 新组件应继承基础样式
3. 保持视觉层次的一致性
4. 测试在不同屏幕尺寸下的表现

### AI 生成新界面时的参考要点
1. 使用本文档中定义的所有 CSS 变量
2. 遵循组件结构规范
3. 保持相同的视觉风格和交互模式
4. 确保新功能与现有设计和谐统一
